<template>
  <div ref="wrapperRef" class="month-picker-wrapper">
    <!-- 显示区域：当前选中月份和图标 -->
    <div class="mp-display" @click="showPicker = !showPicker">
      <span class="mp-date-text">{{ displayText }}</span>
    </div>
  </div>

  <!-- 使用 Teleport 将弹出层渲染到 body 下，避免被父容器裁剪 -->
  <Teleport to="body">
    <div
      v-if="showPicker"
      class="mp-popup"
      :class="placementClass"
      :style="popupStyle"
      @mousedown.stop
    >
      <div class="month-picker">
        <div class="mp-header">
          <button class="mp-btn" @click="prevYear">«</button>
          <span class="mp-title">{{ displayYear }}年</span>
          <button class="mp-btn" @click="nextYear">»</button>
        </div>
        <div class="mp-months">
          <span
            v-for="(month, index) in months"
            :key="index"
            :class="[
              'mp-month',
              isSelected(index) ? 'mp-selected' : '',
              isCurrent(index) ? 'mp-current' : '',
              isDisabled(index) ? 'mp-disabled' : ''
            ]"
            @click="selectMonth(index)"
          >
            {{ month }}
          </span>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import {
  computed,
  ref,
  watch,
  onMounted,
  onBeforeUnmount,
  nextTick
} from "vue";

// 组件 props
const props = defineProps<{
  modelValue: string; // 选中月份，格式 YYYY-MM
  disableFuture?: boolean; // 是否禁用未来月份（包括当月）
  placement?: "top" | "bottom"; // 弹窗方向，默认 top
}>();
const emit = defineEmits(["update:modelValue", "change"]);

// 弹窗位置计算
const popupStyle = ref({});

// 计算弹窗位置
const calculatePopupPosition = () => {
  if (!wrapperRef.value) return;

  const rect = wrapperRef.value.getBoundingClientRect();
  const popupWidth = 260; // 更新月份选择器宽度
  const popupHeight = 240; // 更新月份选择器高度

  let left = rect.left - 50; // 向左偏移50px
  let top =
    props.placement === "bottom"
      ? rect.bottom + 10
      : rect.top - popupHeight - 10;

  // 确保弹窗不超出视口边界
  if (left < 0) left = 10;
  if (left + popupWidth > window.innerWidth) {
    left = window.innerWidth - popupWidth - 10;
  }
  if (top < 0) top = 10;
  if (top + popupHeight > window.innerHeight) {
    top = window.innerHeight - popupHeight - 10;
  }

  popupStyle.value = {
    left: `${left}px`,
    top: `${top}px`
  };
};

// placement 相关
const placementClass = computed(() => {
  return props.placement === "bottom" ? "mp-popup-bottom" : "mp-popup-top";
});

// 控制弹窗显示
const showPicker = ref(false);
const showYearPicker = ref(false);
const wrapperRef = ref<HTMLElement | null>(null);

// 监听弹窗显示状态，计算位置
watch(showPicker, visible => {
  if (visible) {
    nextTick(() => {
      calculatePopupPosition();
    });
  }
});

// 关闭弹窗（点击外部）
function handleClickOutside(e: MouseEvent) {
  if (!wrapperRef.value) return;
  if (!wrapperRef.value.contains(e.target as Node)) {
    showPicker.value = false;
  }
}
onMounted(() => {
  document.addEventListener("mousedown", handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener("mousedown", handleClickOutside);
});

// 月份数组
const months = [
  "1月",
  "2月",
  "3月",
  "4月",
  "5月",
  "6月",
  "7月",
  "8月",
  "9月",
  "10月",
  "11月",
  "12月"
];

// 当前时间
const currentDate = new Date();
const currentYear = currentDate.getFullYear();
const currentMonth = currentDate.getMonth();

// 解析选中的月份
const parseValue = (value: string) => {
  if (!value) return { year: currentYear, month: currentMonth };
  const [year, month] = value.split("-");
  return {
    year: parseInt(year),
    month: parseInt(month) - 1 // 月份从0开始
  };
};

const selectedValue = ref(parseValue(props.modelValue));
const displayYear = ref(selectedValue.value.year);

// 显示文本
const displayText = computed(() => {
  if (!props.modelValue) return `${currentYear}-${currentMonth + 1}`;
  const { year, month } = parseValue(props.modelValue);
  return `${year}-${String(month + 1).padStart(2, "0")}`;
});

// 监听外部 v-model 变化
watch(
  () => props.modelValue,
  val => {
    if (val) {
      selectedValue.value = parseValue(val);
      displayYear.value = selectedValue.value.year;
    }
  }
);

// 判断是否选中
function isSelected(monthIndex: number) {
  return (
    props.modelValue &&
    selectedValue.value.year === displayYear.value &&
    selectedValue.value.month === monthIndex
  );
}

// 判断是否当前月
function isCurrent(monthIndex: number) {
  return displayYear.value === currentYear && monthIndex === currentMonth;
}

// 判断是否禁用
function isDisabled(monthIndex: number) {
  if (!props.disableFuture) return false;

  const targetDate = new Date(displayYear.value, monthIndex, 1);
  const currentDate = new Date(currentYear, currentMonth, 1);

  // 只禁用未来月份，不禁用当月
  return targetDate > currentDate;
}

// 切换年份
function prevYear() {
  displayYear.value--;
}
function nextYear() {
  displayYear.value++;
}

// 选择月份
function selectMonth(monthIndex: number) {
  if (isDisabled(monthIndex)) return;

  const year = displayYear.value;
  const month = monthIndex + 1;
  const value = `${year}-${String(month).padStart(2, "0")}`;

  selectedValue.value = { year, month: monthIndex };
  emit("update:modelValue", value);
  emit("change", value);
  showPicker.value = false; // 选择后自动关闭弹窗
}
</script>

<style scoped>
.month-picker-wrapper {
  position: relative;
  display: inline-block;
}

.mp-display {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: 120px;
  padding: 2px 10px;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  cursor: pointer;
  background: rgb(0 24 75 / 70%);
  border: 1px solid #409eff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgb(64 158 255 / 10%);
  transition: box-shadow 0.2s;
}

.mp-display:hover {
  border-color: #66b1ff;
  box-shadow: 0 4px 16px #409eff44;
}

.mp-date-text {
  width: 100%;
  text-align: center;
  letter-spacing: 1px;
}

.mp-icon {
  font-size: 18px;
  color: #409eff;
}

.mp-popup {
  position: fixed; /* 改为 fixed 定位，相对于视口定位 */
  z-index: 99999; /* 提高z-index层级，确保在大屏页面中显示在最顶层 */
  background: transparent;

  /* left 和 top 通过 JavaScript 动态计算 */
}

.month-picker {
  width: 260px; /* 减小月份选择器宽度 */
  padding: 16px; /* 减小内边距 */
  color: #fff;
  user-select: none;
  background: #001529;
  border: 1px solid #409eff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 20%);
}

.mp-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.mp-title {
  font-size: 16px; /* 减小标题字体 */
  font-weight: bold;
  color: #409eff;
}

.mp-btn {
  padding: 2px 8px; /* 减小按钮内边距 */
  font-size: 16px; /* 减小按钮字体 */
  color: #409eff;
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 4px;
  transition: background 0.2s;
}

.mp-btn:hover {
  background: rgb(64 158 255 / 10%);
}

.mp-months {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px; /* 减小月份间距 */
}

.mp-month {
  padding: 8px 6px; /* 减小月份项内边距 */
  font-size: 13px; /* 减小字体 */
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 4px; /* 减小圆角 */
  transition: all 0.2s;
}

.mp-month:hover:not(.mp-disabled) {
  background: rgb(64 158 255 / 10%);
  border-color: #409eff;
}

.mp-month.mp-current {
  color: #409eff;
  background: rgb(64 158 255 / 20%);
  border-color: #409eff;
}

.mp-month.mp-selected {
  font-weight: bold;
  color: #fff;
  background: #409eff;
}

.mp-month.mp-disabled {
  color: #666;
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
