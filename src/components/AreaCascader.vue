<template>
  <div class="area-cascader-container" :style="containerStyle">
    <el-button
      v-if="showHomeButton"
      class="home-btn"
      type="text"
      title="回到默认节点"
      @click="backToDefaultNode"
    >
      <el-icon :size="36"><House /></el-icon>
    </el-button>
    <el-cascader
      ref="cascaderRef"
      v-model="selectedValue"
      :options="userStore.userRegionTree"
      :props="{
        expandTrigger: 'hover',
        checkStrictly: true,
        label: 'label',
        value: 'id',
        children: 'childList'
      }"
      :placeholder="placeholder"
      clearable
      :popper-class="'custom-cascader-dropdown'"
      :style="cascaderStyle"
      @change="handleChange"
    >
      <template #default="{ data }">
        <div :style="{ padding: '2px 0' }" @click="areaNodeClickHandle(data)">
          {{ data.label }}
        </div>
      </template>
    </el-cascader>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, computed } from "vue";
import { House } from "@element-plus/icons-vue";
import { useUserStoreHook } from "@/store/modules/user";
import { useAppStoreHook } from "@/store/modules/app";
import { getNodePathById } from "@/utils/tree";

const userStore = useUserStoreHook();
const appStore = useAppStoreHook();

interface Props {
  modelValue?: number[];
  showHomeButton?: boolean;
  placeholder?: string;
  // 仅保留宽度可调，其余字体/高度写死
  width?: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  showHomeButton: true,
  width: 260
});

const emit = defineEmits<{
  (e: "update:modelValue", value: number[]): void;
  (e: "change", value: number[]): void;
}>();

const cascaderRef = ref();
const selectedValue = ref<number[]>([]);

// 计算占位符文本
const placeholder = computed(() => {
  if (props.placeholder) {
    return props.placeholder;
  }
  // 使用与原来完全相同的逻辑
  return userStore.userInfo[userStore.getHighestRoleCode] || "选择地区";
});

// 计算主题样式 - 统一使用蓝色主题
const themeStyles = computed(() => {
  return {
    textColor: "#00eaff",
    backgroundColor: "rgb(0, 24, 75, 0.6)",
    borderColor: "rgb(64, 158, 255, 0.3)",
    hoverBorderColor: "#00eaff",
    focusBorderColor: "#00eaff",
    placeholderColor: "#00eaff",
    iconColor: "#00eaff"
  };
});

// 计算容器样式
const containerStyle = computed(() => ({
  height: "36px"
}));

// 计算级联选择器样式
const cascaderStyle = computed(() => ({
  width: `${props.width}px`
}));

// 监听外部传入的值
watch(
  () => props.modelValue,
  newVal => {
    selectedValue.value = newVal || [];
  },
  { immediate: true }
);

// 监听内部值变化
watch(selectedValue, newVal => {
  emit("update:modelValue", newVal);
});

// 处理选择变化
const handleChange = (value: number[]) => {
  selectedValue.value = value;
  emit("change", value);
};

// 地区节点点击事件
const areaNodeClickHandle = (item: any) => {
  const areaPath = getNodePathById(item.id, userStore.userRegionTree);
  const ids = areaPath.map(node => node.id);
  selectedValue.value = ids;

  // 确保在nextTick中关闭下拉框
  nextTick(() => {
    if (cascaderRef.value) {
      cascaderRef.value.togglePopperVisible(false);
    }
    // 然后处理区域变化
    handleChange(ids);
  });
};

// 回到默认节点
const backToDefaultNode = () => {
  // 统一使用用户默认位置逻辑
  const getDefaultNodeId = () => {
    const highestRole = userStore.getHighestRoleCode;
    const defaultValue = userStore.userInfo[highestRole];

    const findNode = (nodes: any[]): number | null => {
      for (const node of nodes) {
        if (node.label === defaultValue) return node.id;
        if (node.childList?.length) {
          const found = findNode(node.childList);
          if (found) return found;
        }
      }
      return null;
    };
    return findNode(userStore.userRegionTree || []);
  };

  const defaultNodeId = getDefaultNodeId();
  if (defaultNodeId) {
    selectedValue.value = [defaultNodeId];
    handleChange([defaultNodeId]);
  }
};

// 暴露方法给父组件
defineExpose({
  areaNodeClickHandle,
  backToDefaultNode
});
</script>

<style lang="scss" scoped>
.area-cascader-container {
  display: flex;
  gap: 8px;
  align-items: center;
  height: 36px;

  .home-btn {
    padding: 4px;
    height: 36px;
    color: rgb(255 255 255 / 80%);
    background: rgb(0 24 75 / 60%);
    border: 1px solid rgb(64 158 255 / 30%);
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      color: #00eaff;
      background: rgb(0 24 75 / 80%);
      border-color: #00eaff;
    }
  }

  /* 节点树选择器样式 */
  :deep(.el-cascader) {
    .el-input {
      --el-input-bg-color: v-bind("themeStyles.backgroundColor");
      --el-input-border-color: v-bind("themeStyles.borderColor");
      --el-input-hover-border-color: v-bind("themeStyles.hoverBorderColor");
      --el-input-focus-border-color: v-bind("themeStyles.focusBorderColor");
      --el-input-text-color: v-bind("themeStyles.textColor");
      --el-input-placeholder-color: v-bind("themeStyles.placeholderColor");

      .el-input__wrapper {
        height: 36px !important;
        background-color: var(--el-input-bg-color);
        box-shadow: none !important;

        &:hover {
          --el-input-bg-color: rgb(0 24 75 / 80%);
        }

        .el-input__inner {
          height: 36px !important;
          font-size: 24px !important;
          line-height: 36px !important;
          color: var(--el-input-text-color) !important;
        }
      }

      .el-input__suffix {
        .el-icon {
          font-size: 24px !important;
          color: v-bind("themeStyles.iconColor") !important;
        }
      }
    }
  }
}

/* 全局强制覆盖节点树字体 - 统一使用蓝色主题 */
#app .area-cascader-container .el-cascader .el-input .el-input__inner {
  color: #00eaff !important;
  font-weight: normal !important;
  font-size: 24px !important;
  line-height: 36px !important;
}

#app .area-cascader-container .el-cascader .el-input .el-input__wrapper {
  background-color: rgb(0, 24, 75, 0.6) !important;
  height: 36px !important;
}

#app
  .area-cascader-container
  .el-cascader
  .el-input
  .el-input__suffix
  .el-icon {
  color: #00eaff !important;
  font-size: 24px !important;
}

/* 级联选择器下拉面板全局样式 - 高优先级覆盖大屏页面样式 */
#app .custom-cascader-dropdown {
  background: rgb(0 24 75 / 95%) !important;
  backdrop-filter: blur(12px);
  border: 1px solid rgb(64 158 255 / 30%) !important;
}

#app .custom-cascader-dropdown .el-popper__arrow::before {
  background: rgb(0 24 75 / 95%) !important;
  border-color: rgb(64 158 255 / 30%) !important;
}

#app .custom-cascader-dropdown .el-cascader-panel {
  background: transparent;
  border: none;
}

#app .custom-cascader-dropdown .el-cascader-menu {
  height: auto;
  max-height: 300px;
  background: transparent;
  border: none;
}

#app .custom-cascader-dropdown .el-cascader-menu:not(:last-child) {
  border-right: 1px solid rgb(64 158 255 / 20%);
}

#app .custom-cascader-dropdown .el-cascader-menu .el-scrollbar__view {
  padding: 4px 0;
}

#app .custom-cascader-dropdown .el-cascader-node {
  padding: 8px 12px;
  font-size: 24px !important;
  line-height: 36px !important;
  color: rgb(255 255 255 / 80%) !important;
}

#app .custom-cascader-dropdown .el-cascader-node .el-cascader-node__label {
  color: rgb(255 255 255 / 80%) !important;
  font-size: 24px !important;
  line-height: 36px !important;
}

#app .custom-cascader-dropdown .el-cascader-node:not(.is-disabled):hover {
  background: rgb(64 158 255 / 10%);
}

#app .custom-cascader-dropdown .el-cascader-node.in-active-path,
#app .custom-cascader-dropdown .el-cascader-node.is-active {
  color: #409eff !important;
  background: rgb(64 158 255 / 20%);
}

#app
  .custom-cascader-dropdown
  .el-cascader-node.in-active-path
  .el-cascader-node__label,
#app
  .custom-cascader-dropdown
  .el-cascader-node.is-active
  .el-cascader-node__label {
  color: #409eff !important;
}

#app .custom-cascader-dropdown .el-cascader-node .el-cascader-node__prefix,
#app .custom-cascader-dropdown .el-cascader-node .el-cascader-node__postfix {
  color: rgb(255 255 255 / 50%) !important;
}

.custom-cascader-dropdown .el-scrollbar__bar {
  background-color: rgb(255 255 255 / 10%);
}

.custom-cascader-dropdown .el-scrollbar__bar .el-scrollbar__thumb {
  background-color: rgb(64 158 255 / 30%);
}

.custom-cascader-dropdown .el-scrollbar__bar .el-scrollbar__thumb:hover {
  background-color: rgb(64 158 255 / 50%);
}
</style>
