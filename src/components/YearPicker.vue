<template>
  <div ref="wrapperRef" class="year-picker-wrapper">
    <!-- 显示区域：当前选中年份和图标 -->
    <div class="yp-display" @click="showPicker = !showPicker">
      <span class="yp-date-text">{{ displayText }}</span>
    </div>
  </div>

  <!-- 使用 Teleport 将弹出层渲染到 body 下，避免被父容器裁剪 -->
  <Teleport to="body">
    <div
      v-if="showPicker"
      class="yp-popup"
      :class="placementClass"
      :style="popupStyle"
      @mousedown.stop
    >
      <div class="year-picker">
        <div class="yp-header">
          <button class="yp-btn" @click="prevDecade">«</button>
          <span class="yp-title"> {{ startYear }}年 - {{ endYear }}年 </span>
          <button class="yp-btn" @click="nextDecade">»</button>
        </div>
        <div class="yp-years">
          <span
            v-for="year in years"
            :key="year"
            :class="[
              'yp-year',
              year === selectedYear ? 'yp-selected' : '',
              year === currentYear ? 'yp-current' : '',
              isDisabled(year) ? 'yp-disabled' : ''
            ]"
            @click="selectYear(year)"
          >
            {{ year }}
          </span>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import {
  computed,
  ref,
  watch,
  onMounted,
  onBeforeUnmount,
  nextTick
} from "vue";

// 组件 props
const props = defineProps<{
  modelValue: string; // 选中年份，格式 YYYY
  disableFuture?: boolean; // 是否禁用未来年份
  placement?: "top" | "bottom"; // 弹窗方向，默认 top
}>();
const emit = defineEmits(["update:modelValue", "change"]);

// 弹窗位置计算
const popupStyle = ref({});

// 计算弹窗位置
const calculatePopupPosition = () => {
  if (!wrapperRef.value) return;

  const rect = wrapperRef.value.getBoundingClientRect();
  const popupWidth = 240; // 年份选择器宽度
  const popupHeight = 220; // 年份选择器高度

  let left = rect.left - 50; // 向左偏移50px
  let top =
    props.placement === "bottom"
      ? rect.bottom + 10
      : rect.top - popupHeight - 10;

  // 确保弹窗不超出视口边界
  if (left < 0) left = 10;
  if (left + popupWidth > window.innerWidth) {
    left = window.innerWidth - popupWidth - 10;
  }
  if (top < 0) top = 10;
  if (top + popupHeight > window.innerHeight) {
    top = window.innerHeight - popupHeight - 10;
  }

  popupStyle.value = {
    left: `${left}px`,
    top: `${top}px`
  };
};

// placement 相关
const placementClass = computed(() => {
  return props.placement === "bottom" ? "yp-popup-bottom" : "yp-popup-top";
});

// 控制弹窗显示
const showPicker = ref(false);
const wrapperRef = ref<HTMLElement | null>(null);

// 监听弹窗显示状态，计算位置
watch(showPicker, visible => {
  if (visible) {
    nextTick(() => {
      calculatePopupPosition();
    });
  }
});

// 关闭弹窗（点击外部）
function handleClickOutside(e: MouseEvent) {
  if (!wrapperRef.value) return;
  if (!wrapperRef.value.contains(e.target as Node)) {
    showPicker.value = false;
  }
}
onMounted(() => {
  document.addEventListener("mousedown", handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener("mousedown", handleClickOutside);
});

// 当前年份
const currentYear = new Date().getFullYear();
const selectedYear = ref(
  props.modelValue ? parseInt(props.modelValue) : currentYear
);
const displayYear = ref(Math.floor(selectedYear.value / 10) * 10);

// 显示文本
const displayText = computed(() => {
  return props.modelValue ? `${props.modelValue}` : `${currentYear}`;
});

// 当前显示的年份范围
const startYear = computed(() => displayYear.value);
const endYear = computed(() => displayYear.value + 9);

// 生成年份数组
const years = computed(() => {
  const arr: number[] = [];
  for (let i = 0; i < 10; i++) {
    arr.push(displayYear.value + i);
  }
  return arr;
});

// 监听外部 v-model 变化
watch(
  () => props.modelValue,
  val => {
    if (val) {
      selectedYear.value = parseInt(val);
      displayYear.value = Math.floor(selectedYear.value / 10) * 10;
    }
  }
);

// 是否禁用
function isDisabled(year: number) {
  return props.disableFuture && year > currentYear;
}

// 切换年代
function prevDecade() {
  displayYear.value -= 10;
}
function nextDecade() {
  displayYear.value += 10;
}

// 选择年份
function selectYear(year: number) {
  if (isDisabled(year)) return;
  selectedYear.value = year;
  emit("update:modelValue", year.toString());
  emit("change", year.toString());
  showPicker.value = false; // 选择后自动关闭弹窗
}
</script>

<style scoped>
.year-picker-wrapper {
  position: relative;
  display: inline-block;
}

.yp-display {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: 120px;
  padding: 2px 10px;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  text-align: center;
  background: rgb(0 24 75 / 60%);
  border: 1px solid rgb(64 158 255 / 30%);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgb(0 24 75 / 80%);
    border-color: rgb(64 158 255 / 60%);
  }
}

.yp-date-text {
  width: 100%;
  text-align: center;
  letter-spacing: 1px;
}

.yp-icon {
  font-size: 18px;
  color: #409eff;
}

.yp-popup {
  position: fixed; /* 改为 fixed 定位，相对于视口定位 */
  z-index: 99999; /* 提高z-index层级，确保在大屏页面中显示在最顶层 */
  background: transparent;

  /* left 和 top 通过 JavaScript 动态计算 */
}

.year-picker {
  width: 240px; /* 减小年份选择器宽度 */
  padding: 16px; /* 减小内边距 */
  color: #fff;
  user-select: none;
  background: #001529;
  border: 1px solid #409eff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 20%);
}

.yp-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px; /* 减小底部间距 */
}

.yp-title {
  font-size: 16px; /* 减小标题字体 */
  font-weight: bold;
  color: #409eff;
}

.yp-btn {
  padding: 2px 8px; /* 减小按钮内边距 */
  font-size: 16px; /* 减小按钮字体 */
  color: #409eff;
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 4px;
  transition: background 0.2s;
}

.yp-btn:hover {
  background: rgb(64 158 255 / 10%);
}

.yp-years {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px; /* 减小年份间距 */
}

.yp-year {
  padding: 8px; /* 减小年份项内边距 */
  font-size: 14px; /* 减小字体 */
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 4px; /* 减小圆角 */
  transition: all 0.2s;
}

.yp-year:hover:not(.yp-disabled) {
  background: rgb(64 158 255 / 10%);
  border-color: #409eff;
}

.yp-year.yp-current {
  color: #409eff;
  background: rgb(64 158 255 / 20%);
  border-color: #409eff;
}

.yp-year.yp-selected {
  font-weight: bold;
  color: #fff;
  background: #409eff;
}

.yp-year.yp-disabled {
  color: #666;
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
