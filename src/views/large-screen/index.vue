<template>
  <div class="screen-wrapper">
    <div ref="screenContainer" class="screen-container">
      <!-- 头部 -->
      <header class="header">
        <div class="header-nav">
          <div class="nav-btn" @click="handleBack">
            <el-icon class="mr-1"><Back /></el-icon>
            进入后台
          </div>
        </div>

        <div class="header-selector">
          <div class="selector-group">
            <AreaCascader
              ref="areaCascaderRef"
              v-model="selectedArea"
              :show-home-button="true"
              :fontSize="26"
              :width="300"
              :height="40"
              @change="handleAreaChange"
            />
          </div>
        </div>

        <div class="header-center">
          <!-- 保留标题装饰，这是折线部分 -->
          <dv-decoration-5
            class="title-decoration"
            :color="['#0066ff', '#00eaff']"
          />
          <h1 class="title">
            宜宾市智能劝导管理系统
            <div class="title-border" />
          </h1>
        </div>

        <div class="header-right">
          <div class="time-box">
            <span class="time">{{ currentTime }}</span>
          </div>
          <div class="user-info">
            <el-badge
              :value="alarmCount"
              :max="99"
              :hidden="alarmCount === 0"
              class="alarm-badge"
            >
              <el-button
                class="alarm-btn"
                type="text"
                @click="showUnhandledAlarms"
              >
                <el-icon :size="32"><Bell /></el-icon>
              </el-button>
            </el-badge>
            <el-dropdown
              trigger="click"
              popper-class="logout-menu"
              @command="handleCommand"
            >
              <div class="user-info-content">
                <el-icon><User /></el-icon>
                <span>{{ userStore.userInfo.name }}</span>
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    command="logout"
                    style="
                      font-size: 20px;
                      color: #00eaff;
                      text-shadow: 0 0 10px rgb(0 234 255 / 30%);
                    "
                    >退出系统</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </header>

      <!-- 内容区 -->
      <main class="main">
        <!-- 左侧面板区域 -->
        <div class="left-panel">
          <div class="panel-item">
            <component
              :is="borderComponents[1 % borderComponents.length]"
              class="panel-border"
            >
              <div
                class="panel-header clickable-panel"
                @click="handleAlarmClick"
              >
                <h3>严重违法预警</h3>
              </div>
              <div class="panel-content">
                <AlarmList :data="alarmData" />
              </div>
            </component>
          </div>
          <div class="panel-item">
            <component
              :is="borderComponents[1 % borderComponents.length]"
              class="panel-border"
            >
              <div
                class="panel-header clickable-panel"
                @click="handleIllegalOverviewClick"
              >
                <div class="header-left">
                  <h3>违法概览</h3>
                  <div class="time-picker-group" @click.stop>
                    <TimeRangeSelector
                      :model-value="
                        illegalOverviewMode === 'day' ? 'day' : 'month'
                      "
                      :enabled-ranges="['day', 'month']"
                      :custom-labels="{ day: '日', month: '月' }"
                      :disable-future="true"
                      :initial-date="illegalOverviewDate"
                      :initial-month="illegalOverviewMonth"
                      @change="handleIllegalOverviewModeChange"
                      @date-change="handleIllegalOverviewDateChange"
                      @month-change="handleIllegalOverviewMonthChange"
                    />
                  </div>
                </div>
              </div>
              <div class="panel-content">
                <IllegalOverview
                  :data="
                    illegalOverviewMode === 'day'
                      ? overviewData
                      : monthOverviewData
                  "
                  :timeRange="illegalOverviewMode"
                  :selectedMonth="
                    illegalOverviewMode === 'month'
                      ? illegalOverviewMonth
                      : undefined
                  "
                  @click="handleIllegalPointClick"
                />
              </div>
            </component>
          </div>
          <div class="panel-item">
            <component
              :is="borderComponents[1 % borderComponents.length]"
              class="panel-border"
            >
              <div
                class="panel-header clickable-panel"
                @click="handleTypeAnalysisClick"
              >
                <div class="header-left">
                  <h3>违法占比</h3>
                  <div class="time-picker-group" @click.stop>
                    <TimeRangeSelector
                      :model-value="typeAnalysisMode"
                      :enabled-ranges="['day', 'month']"
                      :custom-labels="{ day: '日', month: '月' }"
                      :disable-future="true"
                      :initial-date="typeAnalysisDate"
                      :initial-month="typeAnalysisMonth"
                      @change="handleTypeAnalysisModeChange"
                      @date-change="handleTypeAnalysisDateChange"
                      @month-change="handleTypeAnalysisMonthChange"
                    />
                  </div>
                </div>
              </div>
              <div class="panel-content">
                <TypeAnalysis
                  :data="
                    typeAnalysisMode === 'day'
                      ? typeAnalysisData
                      : monthTypeAnalysisData
                  "
                  :time-mode="typeAnalysisMode"
                  :current-date="typeAnalysisDate"
                  :current-month="typeAnalysisMonth"
                  @open-dialog="handleTypeAnalysisDialogOpen"
                />
              </div>
            </component>
          </div>
        </div>

        <div class="center-panel">
          <dv-border-box-11 class="map-container">
            <!-- 移除地图中的直线装饰 -->
            <MapView :points="mapPoints" @point-click="handlePointClick" />
          </dv-border-box-11>

          <div class="statistics-panels">
            <dv-border-box-13 class="stat-panel">
              <AttendanceStatistics :data="attendanceData" />
            </dv-border-box-13>
            <dv-border-box-13 class="stat-panel">
              <PersuasionStatistics
                v-model:visible="persuasionStatisticsVisible"
              />
            </dv-border-box-13>
            <dv-border-box-13 class="stat-panel">
              <HandleStatistics
                :data="handleData"
                @rangeChange="rangeChange"
                @open-dialog="handleOpenDialog"
              />
            </dv-border-box-13>
          </div>
        </div>

        <div class="right-panel">
          <div class="panel-item">
            <component
              :is="borderComponents[1 % borderComponents.length]"
              class="panel-border"
            >
              <div
                class="panel-header clickable-panel"
                @click="handleHotspotClick"
              >
                <div class="header-left">
                  <h3>基础数据</h3>
                </div>
              </div>
              <div class="panel-content">
                <BasicDataPanel />
              </div>
            </component>
          </div>
          <div class="panel-item">
            <component
              :is="borderComponents[1 % borderComponents.length]"
              class="panel-border"
            >
              <div
                class="panel-header clickable-panel"
                @click="handleHotspotClick"
              >
                <div class="header-left">
                  <h3>违法排名</h3>
                  <div class="time-picker-group" @click.stop>
                    <TimeRangeSelector
                      v-model="hotspotMode"
                      :enabled-ranges="['day', 'month']"
                      :custom-labels="{ day: '日', month: '月' }"
                      :disable-future="true"
                      :initial-date="hotspotDate"
                      :initial-month="hotspotMonth"
                      @change="handleHotspotModeChange"
                      @date-change="handleHotspotDateChange"
                      @month-change="handleHotspotMonthChange"
                    />
                  </div>
                </div>
              </div>
              <div class="panel-content">
                <HotspotRank
                  :data="hotspotData"
                  :type="hotspotMode"
                  @itemClick="handleItemClick"
                />
              </div>
            </component>
          </div>

          <div class="panel-item">
            <component
              :is="borderComponents[0 % borderComponents.length]"
              class="panel-border"
            >
              <div
                class="panel-header clickable-panel"
                @click="handleDeviceClick"
              >
                <h3>设备状态</h3>
              </div>
              <div class="panel-content">
                <DeviceOverview :data="deviceData" />
              </div>
            </component>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 将所有弹窗移到screen-wrapper外部，不受缩放影响 -->
  <IllegalDetailDialog
    v-if="illegalDetailVisible"
    :initial-mode="illegalOverviewMode === 'day' ? 'day' : 'month'"
    :initial-date="illegalOverviewDate"
    :initial-month="illegalOverviewMonth"
    :initial-year="
      illegalOverviewMonth ? illegalOverviewMonth.split('-')[0] : undefined
    "
    @close="illegalDetailVisible = false"
  />

  <PersuasionStatisticsDialog
    v-if="persuasionStatisticsVisible"
    v-model:visible="persuasionStatisticsVisible"
    :initial-mode="'month'"
    :initial-month="dayjs().format('YYYY-MM')"
    :initial-year="dayjs().format('YYYY')"
  />

  <PointDetailDialog
    v-if="pointDetailVisible"
    v-model:visible="pointDetailVisible"
    :point-data="currentPointData"
  />

  <TypeAnalysisDialog
    v-if="typeAnalysisDialogVisible"
    v-model:visible="typeAnalysisDialogVisible"
    :initial-mode="typeAnalysisDialogData.timeMode"
    :initial-date="typeAnalysisDialogData.currentDate"
    :initial-month="typeAnalysisDialogData.currentMonth"
    :initial-year="
      typeAnalysisDialogData.currentMonth
        ? typeAnalysisDialogData.currentMonth.split('-')[0]
        : undefined
    "
  />

  <DetailDialog
    v-model:visible="detailDialogVisible"
    :data="detailDialogData"
    :time-type="hotspotMode"
    :selected-date="hotspotMode === 'day' ? hotspotDate : undefined"
    :selected-month="hotspotMode === 'month' ? hotspotMonth : undefined"
    :city="currentItemInfo?.city"
    :county="currentItemInfo?.county"
    :township="currentItemInfo?.township"
    :hamlet="currentItemInfo?.hamlet"
    :site="currentItemInfo?.site"
    :initial-mode="hotspotMode === 'day' ? 'day' : 'month'"
    :initial-date="hotspotDate"
    :initial-month="hotspotMonth"
    :initial-year="hotspotMonth ? hotspotMonth.split('-')[0] : undefined"
  />

  <UnhandledAlarmsDialog
    :visible="alarmsDialogVisible"
    @update:visible="alarmsDialogVisible = $event"
    @update:alarmCount="updateAlarmCount"
    @handle="handleAlarm"
  />

  <statistics-detail-dialog
    v-model:visible="statisticsDialogVisible"
    :dialog-data="statisticsDialogData"
    :initial-mode="illegalOverviewMode === 'day' ? 'day' : 'month'"
    :initial-date="illegalOverviewDate"
    :initial-month="illegalOverviewMonth"
    :initial-year="
      illegalOverviewMonth ? illegalOverviewMonth.split('-')[0] : undefined
    "
  />
</template>

<script setup lang="ts">
// @ts-nocheck
import {
  getMapPointDetail,
  getPointDetailDataByDay,
  getPointDetailDataByMonth
} from "@/api/largeScreen";
import { useAppStoreHook } from "@/store/modules/app";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useUserStoreHook } from "@/store/modules/user";
import { ArrowDown, Back, Bell, House, User } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { getNodePathById } from "../../utils/tree";
import AreaCascader from "@/components/AreaCascader.vue";
import TimeRangeSelector from "@/components/TimeRangeSelector.vue";
import dayjs from "dayjs";
import AlarmList from "./components/AlarmList.vue";
import AttendanceStatistics from "./components/AttendanceStatistics.vue";
import BasicDataPanel from "./components/BasicDataPanel.vue";
import DeviceOverview from "./components/DeviceOverview.vue";
import HandleStatistics from "./components/HandleStatistics.vue";
import HotspotRank from "./components/HotspotRank.vue";
import IllegalOverview from "./components/IllegalOverview.vue";
import MapView from "./components/MapView.vue";
import PersuasionStatistics from "./components/PersuasionStatistics.vue";
import TypeAnalysis from "./components/TypeAnalysis.vue";
import DetailDialog from "./dialogs/DetailDialog.vue";
import IllegalDetailDialog from "./dialogs/IllegalDetailDialog.vue";
import PersuasionStatisticsDialog from "./dialogs/PersuasionStatisticsDialog.vue";
import PointDetailDialog from "./dialogs/PointDetailDialog.vue";
import TypeAnalysisDialog from "./dialogs/TypeAnalysisDialog.vue";
import UnhandledAlarmsDialog from "./dialogs/UnhandledAlarmsDialog.vue";
import { useLargeScreen } from "./hooks";
import { confirmAlarm } from "@/api/alarm";

const {
  currentTime,
  alarmData,
  typeAnalysisData,
  monthTypeAnalysisData,
  overviewData,
  monthOverviewData,
  deviceData,
  violationPoints,
  devicePoints,
  hotspotData,
  handleData,
  attendanceData,
  rangeChange,
  // 新增的数据获取函数
  fetchIllegalOverviewByDate,
  fetchIllegalOverviewByMonth,
  fetchTypeAnalysisByDate,
  fetchTypeAnalysisByMonth,
  fetchPersuasionByDate,
  fetchPersuasionByMonth,
  fetchHotspotByDate,
  fetchHotspotByMonth,
  mapConfig,
  fetchDayIllegalOverview,
  fetchMonthIllegalOverview,
  switchDataType,
  fetchTypeAnalysisData,
  hotspotDataType,
  // 时间状态
  illegalOverviewDate,
  illegalOverviewMonth,
  illegalOverviewMode,
  typeAnalysisDate,
  typeAnalysisMonth,
  typeAnalysisMode,
  hotspotDate,
  hotspotMonth,
  hotspotMode,
  // 时间变化处理方法
  handleIllegalOverviewDateChange,
  handleIllegalOverviewMonthChange,
  handleTypeAnalysisDateChange,
  handleTypeAnalysisMonthChange,
  handleHotspotDateChange,
  handleHotspotMonthChange
} = useLargeScreen();

const router = useRouter();
const userStore = useUserStoreHook();

const dialogVisible = ref(false);
const detailData = ref<any>(null);

const illegalDetailVisible = ref(false);
const persuasionStatisticsVisible = ref(false);
const areaCascaderRef = ref(null);

const pointDetailVisible = ref(false);
const currentPointData = ref<{
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
}>();

const typeAnalysisDialogVisible = ref(false);
const typeAnalysisDialogData = ref<{
  timeMode: "day" | "month" | "year";
  currentDate: string;
  currentMonth: string;
}>({
  timeMode: "day",
  currentDate: "",
  currentMonth: ""
});
// 地区选择值
const selectedArea = ref<number[]>([]);
// 移除，已由hotspotMode替代
const detailDialogVisible = ref(false);
const detailDialogData = ref(null);
const currentItemInfo = ref<{
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
} | null>(null);

const appStore = useAppStoreHook();

let _pollingTimer = null;

const handleIllegalPointClick = data => {
  illegalDetailVisible.value = true;
};

const handleItemClick = async (item: {
  site: string;
  city: string;
  hamlet: string;
  count: number;
  county: string;
  location: string;
  township: string;
  type?: "day" | "month";
}) => {
  try {
    const type = item.type || hotspotMode.value;

    let year: number, month: number, day: number;

    if (type === "month") {
      // 月份模式：使用外部选择的月份
      const [yearStr, monthStr] = hotspotMonth.value.split("-");
      year = parseInt(yearStr);
      month = parseInt(monthStr);
      day = 1; // 月份模式不需要具体日期
    } else {
      // 日模式：使用外部选择的日期
      const dateObj = new Date(hotspotDate.value);
      year = dateObj.getFullYear();
      month = dateObj.getMonth() + 1;
      day = dateObj.getDate();
    }

    const response = await (type === "month"
      ? getPointDetailDataByMonth({
          city: item.city,
          county: item.county,
          township: item.township,
          hamlet: item.hamlet,
          site: item.site,
          year,
          month
        })
      : getPointDetailDataByDay({
          city: item.city,
          county: item.county,
          township: item.township,
          hamlet: item.hamlet,
          site: item.site,
          year,
          month,
          day
        }));

    if (response.code === 200) {
      // 确保数据格式正确
      const typeDetails = Array.isArray(response.data.typeDetails)
        ? response.data.typeDetails
        : [];

      detailDialogData.value = {
        ...response.data,
        location: item.location,
        typeDetails: typeDetails.map(detail => ({
          ...detail,
          violations: detail.violations || detail.totalCount || 0 // 确保有数值
        }))
      };

      // 设置当前点击的地区信息
      currentItemInfo.value = {
        city: item.city,
        county: item.county,
        township: item.township,
        hamlet: item.hamlet,
        site: item.site
      };

      detailDialogVisible.value = true;
    } else {
      console.warn("No data returned from API:", response);
    }
  } catch (error) {
    console.error("Failed to fetch point detail data:", error);
  }
};

// DataV 边框组件配置
const borderComponents = [
  "dv-border-box-1",
  "dv-border-box-2",
  "dv-border-box-3",
  "dv-border-box-4",
  "dv-border-box-5",
  "dv-border-box-6"
];

// DataV 装饰组件配置
const decorationComponents = [
  "dv-decoration-1",
  "dv-decoration-2",
  "dv-decoration-3",
  "dv-decoration-4",
  "dv-decoration-5"
];

const permissionStore = usePermissionStoreHook();

// 修改返回处理函数
const handleBack = () => {
  // 从路由实例中获取所有注册的路由
  const routes = router.getRoutes();

  // 查找 overview 路由
  const overviewRoute = routes.find(route => route.path === "/overview");
  const routeTest = routes[0];
  if (overviewRoute) {
    router.push("/overview");
  } else {
    // 如果没有 overview 路由，跳转到第一个可用路由
    const firstAvailableRoute = routes.find(
      routeItem =>
        routeItem.path !== "*" &&
        routeItem.path !== "/:pathMatch(.*)*" &&
        !routeItem.path.includes(":") &&
        routeItem.path !== route.path
    );
    if (firstAvailableRoute) {
      router.push(firstAvailableRoute.path);
    }
  }
};

// 添加实时报警点击处理方法
const handleAlarmClick = () => {
  router.push({
    path: "/warning/list",
    query: {
      from: "largeScreen"
    }
  });
};

// 添加跳转处理方法
const handleIllegalOverviewClick = () => {
  router.push({
    path: "/illegal/log", // 违法处理页面
    query: { from: "largeScreen" }
  });
};

const handleTypeAnalysisClick = () => {
  router.push({
    path: "/illegal/log", // 违法处理页面
    query: { from: "largeScreen" }
  });
};

const handleTypeAnalysisDialogOpen = (data: {
  timeMode: "day" | "month" | "year";
  currentDate: string;
  currentMonth: string;
}) => {
  typeAnalysisDialogData.value = data;
  typeAnalysisDialogVisible.value = true;
};

const handleHotspotClick = () => {
  router.push({
    path: "/illegal/log",
    query: {
      from: "largeScreen"
    }
  });
};

const handleDeviceClick = () => {
  router.push({
    path: "/system/system-device", // 设备管理页面
    query: { from: "largeScreen" }
  });
};

// 地图点位数据
const mapPoints = ref([]);
const loading = ref(false);

// 获取地图点位数据
const fetchMapPoints = async () => {
  loading.value = true;
  try {
    const params = appStore.getLargeScreenArea;
    const res = await getMapPointDetail(params);
    if (res.code === 200) {
      mapPoints.value = res.data;
    } else {
      ElMessage.error(res.message || "获取地图点位失败");
    }
  } catch (error) {
    console.error("获取地图点位失败:", error);
  } finally {
    loading.value = false;
  }
};

// 监听大屏地址变化，更新地图点位
watch(
  () => appStore.largeScreenArea,
  () => {
    if (appStore.largeScreenArea) {
      fetchMapPoints();
    }
  },
  { immediate: true }
);

// 点击点位处理
const handlePointClick = (point: any) => {
  // 直接使用 point.data 中的数据
  currentPointData.value = point.data;
  pointDetailVisible.value = true;
};

// 添加屏幕自适应缩放相关变量和函数
const screenContainer = ref(null);
const scale = ref(1);
const designWidth = 2560; // 设计稿宽度
const designHeight = 1271; // 设计稿高度

// 计算并应用缩放比例
const calculateScale = () => {
  if (!screenContainer.value) return;

  const clientWidth = document.documentElement.clientWidth;
  const clientHeight = document.documentElement.clientHeight;

  // 计算宽度和高度的缩放比例
  const scaleWidth = clientWidth / designWidth;
  const scaleHeight = clientHeight / designHeight;

  // 使用较小的缩放比例，确保内容完全显示
  scale.value = Math.min(scaleWidth, scaleHeight);

  // 应用缩放和居中
  screenContainer.value.style.transform = `scale(${scale.value})`;

  // 计算缩放后的尺寸
  const scaledWidth = designWidth * scale.value;
  const scaledHeight = designHeight * scale.value;

  // 计算居中所需的位置
  const left = (clientWidth - scaledWidth) / 2;
  const top = (clientHeight - scaledHeight) / 2;

  // 应用位置
  screenContainer.value.style.position = "absolute";
  screenContainer.value.style.left = `${left}px`;
  screenContainer.value.style.top = `${top}px`;

  // 设置CSS变量，供弹窗使用
  document.documentElement.style.setProperty("--scale-ratio", scale.value);
};

// 监听窗口大小变化
const handleResize = () => {
  calculateScale();
};

onMounted(() => {
  document.body.style.overflow = "hidden";
  appStore.initLargeScreenArea();

  // 初始化缩放
  nextTick(() => {
    calculateScale();
    window.addEventListener("resize", handleResize);

    // 轮询获取地图点位
    _pollingTimer = setInterval(() => {
      fetchMapPoints();
    }, 60000);
  });

  var items = document.querySelectorAll(".custom-cascader-dropdown");

  for (var i in items) {
    if (typeof items[i] == "object") {
      items[i].onmouseleave = function () {
        if (areaCascaderRef.value)
          areaCascaderRef.value.togglePopperVisible(false);
      };
    }
  }

  // 初始化用户地区树
  userStore.initUserRegionTree();

  // 监听树形数据变化
  watch(
    () => userStore.getUserRegionTree,
    newVal => {
      if (newVal?.length) {
        // 等树加载完成后再设置默认选中值
        const defaultNodeId = getDefaultNodeId();
        if (defaultNodeId) {
          selectedArea.value = [defaultNodeId];
        }
      }
    },
    { immediate: true }
  );

  // 监听违法概览模式变化，自动触发数据请求
  watch(
    () => illegalOverviewMode.value,
    (newMode, oldMode) => {
      // 只有在模式确实发生变化时才触发
      if (newMode !== oldMode && oldMode !== undefined) {
        if (newMode === "month") {
          // 切换到月模式，使用当前选中的月份请求数据
          handleIllegalOverviewMonthChange(illegalOverviewMonth.value);
        } else if (newMode === "day") {
          // 切换到日模式，使用当前选中的日期请求数据
          handleIllegalOverviewDateChange(illegalOverviewDate.value);
        }
      }
    }
  );

  // 监听违法类型分析模式变化，自动触发数据请求
  watch(
    () => typeAnalysisMode.value,
    (newMode, oldMode) => {
      // 只有在模式确实发生变化时才触发
      if (newMode !== oldMode && oldMode !== undefined) {
        if (newMode === "month") {
          // 切换到月模式，使用当前选中的月份请求数据
          handleTypeAnalysisMonthChange(typeAnalysisMonth.value);
        } else if (newMode === "day") {
          // 切换到日模式，使用当前选中的日期请求数据
          handleTypeAnalysisDateChange(typeAnalysisDate.value);
        }
      }
    }
  );

  // 监听高发地段排名模式变化，自动触发数据请求
  watch(
    () => hotspotMode.value,
    (newMode, oldMode) => {
      // 只有在模式确实发生变化时才触发
      if (newMode !== oldMode && oldMode !== undefined) {
        if (newMode === "month") {
          // 切换到月模式，使用当前选中的月份请求数据
          handleHotspotMonthChange(hotspotMonth.value);
        } else if (newMode === "day") {
          // 切换到日模式，使用当前选中的日期请求数据
          handleHotspotDateChange(hotspotDate.value);
        }
      }
    }
  );
});

onUnmounted(() => {
  document.body.style.overflow = "auto";
  window.removeEventListener("resize", handleResize);
  if (_pollingTimer) clearInterval(_pollingTimer);
});

// 添加鼠标跟随效果
onMounted(() => {
  const panels = document.querySelectorAll(".clickable-panel");

  panels.forEach(panel => {
    panel.addEventListener("mousemove", (e: MouseEvent) => {
      const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;

      (e.currentTarget as HTMLElement).style.setProperty("--mouse-x", `${x}%`);
      (e.currentTarget as HTMLElement).style.setProperty("--mouse-y", `${y}%`);
    });
  });
});

// 履职情况 - 支持日期和月份选择（暂时保留在组件中，因为hooks中还没有履职相关的完整实现）
const persuasionDate = ref(new Date().toISOString().split("T")[0]);
const persuasionMonth = ref(
  `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}`
);
const persuasionMode = ref<"date" | "month">("date");

// 履职情况时间变化处理
const handlePersuasionDateChange = (date: string) => {
  persuasionDate.value = date;
  fetchPersuasionByDate(date);
};

const handlePersuasionMonthChange = (month: string) => {
  persuasionMonth.value = month;
  fetchPersuasionByMonth(month);
};

// 添加模式变化处理函数
const handleIllegalOverviewModeChange = (mode: string) => {
  illegalOverviewMode.value = mode as "day" | "month";
};

const handleTypeAnalysisModeChange = (mode: string) => {
  typeAnalysisMode.value = mode as "day" | "month";
};

const handleHotspotModeChange = (mode: string) => {
  hotspotMode.value = mode;
};

// 处理地区选择变化
const handleAreaChange = (value: number[]) => {
  selectedArea.value = value;
  const params = getAddressParams(value);
  appStore.setLargeScreenArea(params, value);
};

// 地区节点点击事件已移至 AreaCascader 组件内部处理

// 获取地址参数
const getAddressParams = (areaIds: number[]): RequestParams => {
  const params: RequestParams = {
    city: useUserStoreHook().userInfo.city
  };
  if (!areaIds?.length) return params;
  let currentNode = userStore.userRegionTree.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return {};
  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }
  return params;
};

// 根据ID查找节点
const findNodeById = (
  nodes: TreeNode[] | null,
  id: number
): TreeNode | null => {
  if (!nodes) return null;

  for (const node of nodes) {
    if (node.id === id) return node;
    const found = findNodeById(node.childList, id);
    if (found) return found;
  }

  return null;
};

// 根据用户最高权限获取对应的节点ID
const getDefaultNodeId = () => {
  const highestRole = userStore.getHighestRoleCode;
  const defaultValue = userStore.userInfo[highestRole];

  // 在树中查找匹配的节点
  const findNode = nodes => {
    for (const node of nodes) {
      if (node.label === defaultValue) {
        return node.id;
      }
      if (node.childList?.length) {
        const found = findNode(node.childList);
        if (found) return found;
      }
    }
    return null;
  };
  return findNode(userStore.getUserRegionTree);
};

const handleCommand = (command: string) => {
  if (command === "logout") {
    userStore.logOut();
    router.push("/login");
  }
};

// 返回默认节点方法已移至 AreaCascader 组件内部处理

// 未处理的报警数据
const unhandledAlarms = ref([]);
const alarmsDialogVisible = ref(false);
const alarmCount = ref(0);

const updateAlarmCount = (count: number) => {
  alarmCount.value = count;
};

const showUnhandledAlarms = () => {
  alarmsDialogVisible.value = true;
};

const handleAlarm = async alarm => {
  try {
    // 调用后端API处理报警
    const res = await confirmAlarm(alarm.id);

    if (res.code === 200) {
      ElMessage.success(处理了报警);
      unhandledAlarms.value = unhandledAlarms.value.filter(
        item => item.id !== alarm.id
      );
      if (unhandledAlarms.value.length === 0) {
        alarmsDialogVisible.value = false;
      }
    } else {
      ElMessage.error("处理报警失败");
    }
  } catch (error) {
    ElMessage.error("处理报警失败");
    console.error("处理报警失败详情:", error);
  }
};

// 弹窗控制 - 确保初始值为 false
const statisticsDialogVisible = ref(false);
const statisticsDialogData = ref({
  dialogType: "",
  title: "",
  range: "week"
});

// 处理打开弹窗事件
const handleOpenDialog = (type, data) => {
  statisticsDialogData.value = {
    ...data,
    dialogType: type
  };
  statisticsDialogVisible.value = true;
};

// 旧的时间范围选择逻辑已移除，使用新的日期/月份选择器
</script>

<style lang="scss">
// 全局背景覆盖
body,
#app {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
  background: rgb(15 42 89) !important;
}

// Element Plus 对话框相关样式覆盖
.el-overlay-dialog {
  background-color: transparent !important;
}

.el-dialog__wrapper {
  background-color: transparent !important;
}

.el-dialog__headerbtn:hover .el-dialog__close {
  color: #409eff !important;
}

// 修复弹窗样式
.el-dialog {
  --el-dialog-margin-top: 15vh;

  margin: var(--el-dialog-margin-top) auto 50px !important;
}

// 确保弹窗遮罩层全屏
.el-overlay {
  position: fixed !important;
  inset: 0 !important;
  z-index: 2000 !important;
  height: 100% !important;
  overflow: auto !important;
  background-color: var(--el-overlay-color-lighter) !important;
}

// 大屏页面的级联选择器样式现在由 AreaCascader 组件内部处理

// 级联选择器下拉菜单样式现在由 AreaCascader 组件内部处理

// 级联选择器输入框样式现在由 AreaCascader 组件内部处理

// 限制覆盖Element Plus的静态样式类只应用于大屏页面
.screen-wrapper .el-input.is-disabled .el-input__inner,
.screen-wrapper .el-input__inner.is-disabled {
  color: #00eaff !important;
  -webkit-text-fill-color: #00eaff !important;
}

.logout-menu {
  background: rgb(0 24 75 / 95%) !important;
  border: 1px solid rgb(64 158 255 / 30%) !important;

  .el-dropdown-menu__item:not(.is-disabled):focus,
  .el-dropdown-menu__item:not(.is-disabled):hover {
    background: transparent !important;
  }

  .el-dropdown-menu {
    background: transparent !important;

    .el-dropdown-menu__item {
      color: rgb(255 255 255 / 70%);
    }
  }

  .el-popper__arrow::before {
    background: rgb(0 24 75 / 95%) !important;
    border-color: rgb(64 158 255 / 30%) !important;
  }
}

// 级联选择器下拉菜单样式现在由 AreaCascader 组件内部处理

// 级联选择器样式现在由 AreaCascader 组件内部处理

// 所有级联选择器样式现在由 AreaCascader 组件内部统一处理
</style>

<style lang="scss" scoped>
@import "./styles/font-sizes.scss";
@keyframes borderPulse {
  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.6;
  }

  100% {
    opacity: 0.3;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }

  50% {
    left: 100%;
  }

  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.6;
  }

  100% {
    opacity: 0.3;
  }
}

.screen-wrapper {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: radial-gradient(circle at center, #001529 0%, #000c25 100%);
}

.screen-container {
  position: absolute;
  position: relative;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  width: 2560px; // 设计稿宽度
  height: 1271px; // 设计稿高度
  overflow: hidden; /* 恢复 hidden，保持原有布局 */
  color: #fff;
  background: radial-gradient(circle at center, #001529 0%, #000c25 100%);
  transform-origin: 0 0;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: linear-gradient(
      to bottom,
      rgb(0 12 37 / 90%) 0%,
      rgb(0 24 75 / 90%) 100%
    );
  }

  .header {
    position: relative;
    z-index: 1;
    height: 80px;
    padding: 0 32px;
    background: linear-gradient(
      90deg,
      rgb(0 24 75 / 0%) 0%,
      rgb(0 24 75 / 50%) 25%,
      rgb(0 24 75 / 50%) 75%,
      rgb(0 24 75 / 0%) 100%
    );
    border-bottom: 1px solid rgb(0 110 255 / 30%);

    &::before,
    &::after {
      position: absolute;
      width: 200px;
      height: 2px;
      content: "";
      background: linear-gradient(
        to right,
        transparent,
        rgb(0 110 255 / 50%),
        transparent
      );
    }

    &::before {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    &::after {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    .header-nav {
      position: absolute;
      top: 0;
      left: 20px;
      z-index: 11;
      display: flex;
      align-items: center;

      .nav-btn {
        position: relative;
        z-index: 11;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 3px 16px;
        height: 36px;
        margin-top: 42px;
        font-size: 24px;
        color: #00eaff;
        cursor: pointer;
        background: rgb(0 110 255 / 20%);
        border: 1px solid rgb(0 234 255 / 30%);
        border-radius: 4px;
        box-shadow: none;
        transition: all 0.3s ease;

        &:hover {
          background: rgb(0 110 255 / 30%);
          border-color: rgb(0 234 255 / 50%);
          box-shadow: 0 0 15px rgb(0 234 255 / 30%);
        }

        .el-icon {
          font-size: 24px;
        }
      }
    }

    .header-selector {
      position: absolute;
      top: 0;
      left: 200px;
      z-index: 10;
      display: flex;
      align-items: center;
      margin-top: 10px;
      .selector-group {
        display: flex;
        align-items: center;
        margin-top: 32px;
        font-size: 24px;
      }
    }

    .header-center {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 2;
      transform: translate(-50%, -50%);

      .title {
        position: relative;
        padding: 0 60px;
        font-family: Play, sans-serif;
        font-size: 48px;
        font-weight: 700;
        color: transparent;
        text-align: center;
        text-shadow:
          0 0 10px rgb(0 234 255 / 30%),
          0 0 20px rgb(0 234 255 / 20%);
        white-space: nowrap;
        background: linear-gradient(to bottom, #fff, #00eaff);
        background-clip: text;

        &::before,
        &::after {
          position: absolute;
          top: 50%;
          width: 50px;
          height: 20px;
          content: "";
          background-size: contain;
        }

        &::before {
          left: 0;
          transform: translateY(-50%);
        }

        &::after {
          right: 0;
          transform: translateY(-50%) rotate(180deg);
        }
      }
    }

    .header-right {
      position: absolute;
      top: 0;
      right: 20px;
      display: flex;
      gap: 16px;
      align-items: center;

      .time-box {
        min-width: 380px;
        padding: 8px 16px;
        margin-top: 26px;
        text-align: left;

        .time {
          display: inline-block;
          font-family: Orbitron, sans-serif;
          font-size: 24px;
          font-weight: 500;
          color: #00eaff;
          text-shadow:
            0 0 10px rgb(0 234 255 / 30%),
            0 0 20px rgb(0 234 255 / 20%),
            0 0 30px rgb(0 234 255 / 10%);
          letter-spacing: 2px;
        }
      }

      .user-info {
        .user-info-content {
          display: flex;
          gap: 8px;
          align-items: center;
          padding: 8px 16px;
          height: 36px;
          margin-top: 42px;
          font-family: Play, sans-serif;
          font-size: 24px;
          color: #fff;
          letter-spacing: 1px;
          cursor: pointer;
          background: rgb(0 110 255 / 20%);
          border: 1px solid rgb(0 234 255 / 30%);
          border-radius: 4px;
          transition: all 0.3s;

          &:hover {
            background: rgb(0 110 255 / 30%);
            border-color: rgb(0 234 255 / 50%);
            box-shadow: 0 0 15px rgb(0 234 255 / 30%);
          }

          .el-icon {
            margin-right: 4px;
            font-size: 26px;
            color: #00eaff;
          }

          span {
            font-size: 26px;
            color: #00eaff;
            text-shadow: 0 0 10px rgb(0 234 255 / 30%);
          }

          .el-icon--right {
            margin-left: 4px;
            font-size: 26px;
            transition: transform 0.3s;
          }
        }
      }
    }

    .header-left-decoration,
    .header-right-decoration {
      position: absolute;
      top: 0;
      width: 30%;
      height: 60px;
    }

    .header-left-decoration {
      left: 0;
    }

    .header-right-decoration {
      right: 0;
      transform: rotateY(180deg);
    }

    .title-decoration {
      position: absolute;
      top: -20px;
      left: 0;
      width: 100%;
      height: 8px;
    }
  }

  .main {
    position: relative;
    z-index: 1;
    display: flex;
    flex: 1;
    gap: 16px;
    padding: 16px;
    overflow: hidden; /* 恢复 hidden，保持原有布局 */

    .left-panel,
    .right-panel {
      display: flex;
      flex-direction: column;
      gap: 16px;
      width: 400px;

      /* 移除 overflow 设置，使用默认值 */

      > div:first-child {
        .panel-border {
          animation: none;
        }

        .panel-header {
          justify-content: center;

          h3 {
            width: 100%;
            padding-left: 0;
            text-align: center;

            &::before {
              display: none;
            }
          }
        }

        .header-decoration {
          display: none;
        }
      }

      > div:last-child {
        flex: 1;
        height: auto !important;
        min-height: 0;
      }
    }

    .right-panel > div:last-child {
      flex: 1;
      height: auto !important;
      min-height: 0;
    }

    .center-panel {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 16px;

      .map-container {
        flex: 1;
        min-height: 0;
        background: rgb(0 24 75 / 60%);
        border: 1px solid rgb(0 110 255 / 30%);
        border-radius: 4px;
        box-shadow: 0 0 20px rgb(0 110 255 / 10%);
      }

      .bottom-panel {
        position: relative;
        height: 240px;
        background: rgb(0 24 75 / 60%);
        border-radius: 4px;
        box-shadow: 0 0 20px rgb(0 110 255 / 10%);
      }
    }
  }
}

.panel-item {
  position: relative;

  &:not(.left-panel > div:last-child, .right-panel > div:last-child) {
    height: calc((100% - 112px) / 3);
  }

  .panel-header {
    position: relative;
    display: flex;
    align-items: center;
    height: 45px;
    padding: 8px 16px 0;
    background: linear-gradient(
      90deg,
      rgb(0 110 255 / 10%),
      rgb(0 110 255 / 20%),
      rgb(0 110 255 / 10%)
    );

    &::before,
    &::after {
      position: absolute;
      left: 0;
      width: 100%;
      height: 1px;
      content: "";
      background: linear-gradient(
        90deg,
        transparent,
        rgb(0 110 255 / 50%),
        transparent
      );
    }

    &::before {
      top: 0;
    }

    &::after {
      bottom: 0;
    }

    h3 {
      position: relative;
      z-index: 1;
      padding-left: 12px;
      font-size: 24px;
      font-weight: 500;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        content: "";
        background: #06f;
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }
  }

  .panel-content {
    height: calc(100% - 40px);
    padding: 16px;
    overflow: hidden; /* 恢复 hidden，保持滚动功能 */
  }

  .panel-border {
    width: 100%;
    height: 100%;
    background: rgb(0 24 75 / 60%);

    /* 移除 overflow 设置，使用默认值 */
  }

  .header-decoration {
    position: absolute;
    top: 18px;
    left: 60px;
    width: 50%;
    height: 4px;
    animation: rotate 3s linear infinite;
  }

  &:hover {
    .panel-border {
      box-shadow: 0 0 15px rgb(64 158 255 / 30%);
      transition: all 0.3s;
    }
  }
}

.footer-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
}

.panel-item {
  .panel-border {
    animation: glow 3s infinite;
  }
}

.header {
  .header-left-decoration,
  .header-right-decoration {
    animation: borderPulse 3s infinite;
  }

  .title {
    position: relative;
    overflow: hidden;

    &::after {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      content: "";
      background: linear-gradient(
        90deg,
        transparent,
        rgb(255 255 255 / 20%),
        transparent
      );
      animation: shine 3s infinite;
    }
  }
}

.map-container {
  position: relative;
  overflow: hidden;

  &::before {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    content: "";
    background: radial-gradient(
      circle at center,
      rgb(0 110 255 / 10%) 0%,
      transparent 70%
    );
    animation: rotate 20s linear infinite;
  }

  .map-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0.5;
    animation: glow 4s infinite;
  }
}

.bottom-panel {
  .bottom-decoration {
    animation: float 3s ease-in-out infinite;
  }
}

.screen-container::after {
  position: absolute;
  inset: 0;
  pointer-events: none;
  content: "";
  background: radial-gradient(
    circle at 50% 50%,
    rgb(0 110 255 / 10%),
    transparent 50%
  );
  animation: pulse 4s infinite;
}

.statistics-panels {
  display: flex;
  gap: 16px;
  height: calc((100% - 80px) / 3);
  min-height: 0;

  .stat-panel {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100%;
    min-height: 0;

    :deep(.dv-border-box-13) {
      display: flex;
      flex: 1;
      flex-direction: column;

      .dv-border-box-content {
        flex: 1;
        min-height: 0;
      }
    }
  }
}

.type-detail {
  padding: 12px;
  margin-bottom: 16px;
  color: #fff;
  background: rgb(0 24 75 / 80%);
  border-radius: 8px;
  box-shadow: 0 0 10px rgb(0 110 255 / 50%);
}

.panel-header {
  padding: 14px 16px;
  border-bottom: 1px solid rgb(64 158 255 / 20%);

  &.clickable-panel {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;

    &::before {
      position: absolute;
      inset: 0;
      pointer-events: none;
      content: "";
      background: radial-gradient(
        circle at var(--x, 50%) var(--y, 50%),
        rgb(64 158 255 / 15%) 0%,
        transparent 100%
      );
      opacity: 0;
      transition: opacity 0.3s;
    }

    &:hover {
      background: rgb(64 158 255 / 10%);

      &::before {
        opacity: 1;
      }

      h3 {
        color: #409eff;
        transform: translateX(4px);
      }
    }
  }

  h3 {
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;

    &::after {
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 0;
      height: 2px;
      content: "";
      background: #409eff;
      transition: width 0.3s ease;
    }
  }

  &.clickable-panel:hover h3::after {
    width: 100%;
  }
}

.panel-item {
  overflow: hidden; /* 恢复 hidden，保持原有样式 */
  background: rgb(0 24 75 / 40%);
  border: 1px solid rgb(0 110 255 / 20%);
  border-radius: 4px;
}

// 添加鼠标跟随效果
.clickable-panel {
  &:hover {
    --x: var(--mouse-x, 50%);
    --y: var(--mouse-y, 50%);
  }
}

.stat-panel {
  &:hover {
    box-shadow: 0 0 15px rgb(64 158 255 / 30%);
    transition: all 0.3s;
  }
}

.panel-header {
  &.clickable-panel {
    .header-left {
      display: flex;
      flex: 1;
      gap: 6px;
      align-items: center;
      justify-content: space-between;

      h3 {
        margin: 0;
      }

      .time-picker-group {
        position: relative;
        z-index: 1;
        display: flex;
        gap: 6px;
        align-items: center;
        // picker-toggle 样式已删除，现在使用 TimeRangeSelector 组件
      }

      // 旧的 time-range-selector 样式已删除，现在使用 TimeRangeSelector 组件
    }
  }
}

.user-info {
  display: flex;
  gap: 16px;
  align-items: center;

  .alarm-badge {
    transform: translateY(18px);

    svg {
      color: #00eaff;
    }

    :deep(.el-badge__content) {
      background-color: #f56c6c;
    }
  }

  .alarm-btn {
    padding: 8px;
    color: rgb(255 255 255 / 80%);
    transition: all 0.3s;

    &:hover {
      color: #f56c6c;
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.9);
    }
  }
}

/* 全局确保日期选择器弹窗显示在最顶层 */
.cdp-popup,
.mp-popup,
.yp-popup {
  position: fixed !important; /* 使用 fixed 定位，相对于视口定位，配合 Teleport 使用 */
  z-index: 99999 !important;
}

/* 全局优化ECharts tooltip样式，确保不被遮挡 */
.echarts-tooltip {
  z-index: 99998 !important;
  pointer-events: none !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important;
}

/* 确保所有ECharts容器的tooltip都有正确的层级 */
div[_echarts_instance_] + div {
  z-index: 99998 !important;
}
</style>
