// 标题类字体
$font-main-title: 48px;
$font-panel-title: 24px;

// 导航和控制元素
$font-nav-button: 24px;
$font-nav-icon: 24px;
$font-time-display: 24px;
$font-user-info: 24px;

// 数据显示
$font-data-label: 16px;
$font-data-value: 24px;
$font-data-value-large: 26px;
$font-data-value-small: 16px;

// 时间相关
$font-time-switcher: 18px;
$font-time-item: 40px;

// 基础字体
$font-base: 16px;
$font-small: 14px;
$font-medium: 18px;
$font-large: 20px;

// 大屏组件标题
$font-component-title: 26px;
//大屏弹窗组件标题
$font-dialog-title: 24px;

// 组件时间选择器
$font-time-picker: 24px;
//弹窗关闭按钮图标字体大小
$font-illegal-close: 40px;

// 报警字体
// 报警时间与地点
$font-alarm-time: 18px;
// 违法行为类型
$font-alarm-type: 22px;

// 违法概览字体
// 违法总数字体
$font-illegal-total: 22px;
// 弹窗Tab选项卡文字字体大小
$font-illegal-tab: 24px;
// 弹窗分时统计总违法数字体
$font-illegal-tab-value: 22px;

// 违法类型分析字体
// 违法类型分析总数统计字体
$font-illegal-type-total: 24px;
// 违法类型分析图例项字体
$font-illegal-type-legend: 24px;

// 上岗情况字体
// 上岗情况弹窗表格类型选择器按钮字体大小（列表选择）
$font-attendance-detail-table-type-selector: 28px;
// 上岗情况弹窗分页器字体大小
$font-attendance-detail-pagination: 22px;
// 上岗情况弹窗表格表头字体大小
$font-attendance-detail-table-header: 22px;
// 其余状态字体大小
$font-attendance-detail-other-status: 30px;
//时间轴组件弹窗字体标题
$font-timeline-tooltip-title: 20px;
//时间轴组件弹窗字体内容
$font-timeline-tooltip-content: 16px;
//勤务安排详细弹窗
//勤务安排详细弹窗统计卡片标签字体大小
$font-attendance-detail-card-label: 26px;
//勤务安排详细弹窗表格字体大小
$font-efficiency-table: 22px;

// 精准劝导概览字体
// 统计信息标签("任务总数" 、"总完成数")字体大小
$font-persuasion-value: 22px;
// 统计信息标签("任务总数" 、"总完成数")数值大小
$font-persuasion-value-number: 26px;
//精准劝导弹窗表格标题
$font-persuasion-table-title: 24px;
//精准劝导弹窗表格内容字体大小
$font-persuasion-table: 22px;
//精准劝导弹窗表格展开表格表头字体大小
$font-persuasion-table-expand-header: 18px;
//表格展开提示文字字体大小
$font-persuasion-table-expand-hint: 14px;
//表格加载提示文字字体大小
$font-persuasion-table-loading-text: 16px;


// 违法排名字体
// 违法排名字体大小
$font-illegal-rank: 20px;
//违法排名弹窗
//违法排名弹窗卡片字体大小
$font-illegal-rank-card: 24px;
//违法排名弹窗卡片数值字体大小
$font-illegal-rank-card-value: 26px;

// 设备概览字体
// 设备统计标签("点位"、"正常"、"异常")字体大小
$font-device-label: 18px;
// 设备统计数值字体大小
$font-device-value: 26px;
// 设备状态相关文字字体大小
$font-device-status: 17px;

//地图弹窗字体
//弹窗子标题字体大小
$font-map-dialog-sub-title: 22px;
//地图弹窗表头字体大小
$font-map-dialog-table-header: 22px;
//地图弹窗表格内容字体大小
$font-map-dialog-table-content: 20px;
//地图弹窗班次信息卡片字体大小
$font-map-dialog-shift-info-card: 22px;
//地图弹窗班次字体大小
$font-map-dialog-shift-name: 20px;
//地图弹窗违法类型统计违法名称字体大小
$font-map-dialog-illegal-name: 20px;
//地图弹窗违法类型统计违法类型数量字体大小
$font-map-dialog-illegal-type-value: 24px;
//无数据状态文字字体
$font-map-dialog-no-data-text: 26px;

// 报警弹窗字体
$font-alarm-dialog-title: 22px;
//占位符文字大小(暂无数据)
$font-map-dialog-no-data-text: 26px;






// 工具类
.fs-panel-title { font-size: $font-panel-title !important; }
.fs-data-value { font-size: $font-data-value !important; }
.fs-data-label { font-size: $font-data-label !important; }
.fs-nav-button { font-size: $font-nav-button !important; }
.fs-time-item { font-size: $font-time-item !important; }
.fs-base { font-size: $font-base !important; }
.fs-small { font-size: $font-small !important; }
.fs-medium { font-size: $font-medium !important; }
.fs-large { font-size: $font-large !important; }
.fs-alarm-time { font-size: $font-alarm-time !important; }
.fs-alarm-type { font-size: $font-alarm-type !important; }
.fs-illegal-total { font-size: $font-illegal-total !important; }
.fs-component-title { font-size: $font-component-title !important; }
.fs-time-picker { font-size: $font-time-picker !important; }
.fs-persuasion-value { font-size: $font-persuasion-value !important; }
.fs-persuasion-value-number { font-size: $font-persuasion-value-number !important; }
.fs-illegal-rank { font-size: $font-illegal-rank !important; }
.fs-device-label { font-size: $font-device-label !important; }
.fs-device-value { font-size: $font-device-value !important; }
.fs-device-status { font-size: $font-device-status !important; }
.fs-illegal-tab { font-size: $font-illegal-tab !important; }
.fs-illegal-close { font-size: $font-illegal-close !important; }
.fs-dialog-title { font-size: $font-dialog-title !important; }
.fs-illegal-tab-value { font-size: $font-illegal-tab-value !important; }
.fs-illegal-type-total { font-size: $font-illegal-type-total !important; }
.fs-illegal-type-legend { font-size: $font-illegal-type-legend !important; }
.fs-attendance-detail-table-type-selector { font-size: $font-attendance-detail-table-type-selector !important; }
.fs-attendance-detail-pagination { font-size: $font-attendance-detail-pagination !important; }
.fs-attendance-detail-table-header { font-size: $font-attendance-detail-table-header !important; }
.fs-attendance-detail-other-status { font-size: $font-attendance-detail-other-status !important; }
.fs-timeline-tooltip-title { font-size: $font-timeline-tooltip-title !important; }
.fs-timeline-tooltip-content { font-size: $font-timeline-tooltip-content !important; }
.fs-attendance-detail-card-label { font-size: $font-attendance-detail-card-label !important; }
.fs-efficiency-table { font-size: $font-efficiency-table !important; }
.fs-persuasion-table-title { font-size: $font-persuasion-table-title !important; }
.fs-persuasion-table-expand-hint { font-size: $font-persuasion-table-expand-hint !important; }
.fs-persuasion-table-loading-text { font-size: $font-persuasion-table-loading-text !important; }
.fs-persuasion-table { font-size: $font-persuasion-table !important; }
.fs-persuasion-table-expand-header { font-size: $font-persuasion-table-expand-header !important; }
.fs-illegal-rank-card { font-size: $font-illegal-rank-card !important; }
.fs-illegal-rank-card-value { font-size: $font-illegal-rank-card-value !important; }
.fs-map-dialog-table-header { font-size: $font-map-dialog-table-header !important; }
.fs-map-dialog-sub-title { font-size: $font-map-dialog-sub-title !important; }
.fs-map-dialog-table-content { font-size: $font-map-dialog-table-content !important; }
.fs-map-dialog-illegal-name { font-size: $font-map-dialog-illegal-name !important; }
.fs-map-dialog-illegal-type-value { font-size: $font-map-dialog-illegal-type-value !important; }
.fs-map-dialog-shift-info-card { font-size: $font-map-dialog-shift-info-card !important; }
.fs-map-dialog-shift-name { font-size: $font-map-dialog-shift-name !important; }
.fs-map-dialog-no-data-text { font-size: $font-map-dialog-no-data-text !important; }
.fs-alarm-dialog-title { font-size: $font-alarm-dialog-title !important; }
.fs-alarm-dialog-no-data-text { font-size: $font-map-dialog-no-data-text !important; }
