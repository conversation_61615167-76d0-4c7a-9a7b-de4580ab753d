<template>
  <!-- 视频对话框 -->
  <el-dialog
    v-if="!isMinimized"
    v-model="dialogVisible"
    :show-close="false"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    class="large-screen-alarm-video-dialog"
  >
    <template #header>
      <div class="dialog-header">
        <div class="title">
          <el-icon><VideoCamera /></el-icon>
          <span>实时监控</span>
        </div>
        <div v-if="props.devices.length > 1" class="video-tabs">
          <el-radio-group v-model="currentVideoIndex">
            <el-radio-button
              v-for="(device, index) in props.devices"
              :key="index"
              :label="index"
            >
              {{ device.deviceName }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="dialog-controls">
          <el-button @click="toggleMinimize">
            <el-icon><Remove /></el-icon>
          </el-button>
          <el-button @click="handleClose">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </template>
    <div class="video-container">
      <video
        ref="videoRef"
        class="video-player"
        controls
        autoplay
        muted
        webkit-playsinline
        playsinline
      />
    </div>
    <WebRtc
      v-if="visible && devices[currentVideoIndex]"
      ref="webRtcRef"
      :roomId="devices[currentVideoIndex].equipmentNumber"
    />
  </el-dialog>

  <!-- 最小化时显示的浮动窗口 -->
  <div v-else class="minimized-video" @click="toggleMinimize">
    <video
      ref="minimizedVideoRef"
      class="video-player"
      muted
      autoplay
      webkit-playsinline
      playsinline
    />
    <div class="minimized-controls">
      <span>实时监控</span>
      <el-button @click.stop="toggleMinimize">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import WebRtc from "../../../components/WebRtc/index.vue";
import { Close, Remove, VideoCamera } from "@element-plus/icons-vue";
import flvjs from "flv.js";
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";

const props = defineProps<{
  devices: Array<{
    id: number;
    deviceName: string;
    streamKey: string;
    equipmentNumber: string;
  }>;
  visible: boolean;
}>();

const emit = defineEmits<{
  "update:visible": [value: boolean];
}>();

const dialogVisible = ref(props.visible);
const videoRef = ref<HTMLVideoElement | null>(null);
const minimizedVideoRef = ref<HTMLVideoElement | null>(null);
const isMinimized = ref(false);
const currentVideoIndex = ref(0);
let flvPlayer: flvjs.Player | null = null;

// 初始化播放器
const initPlayer = () => {
  if (!flvjs.isSupported()) {
    console.error("浏览器不支持 flv.js");
    return;
  }

  const currentDevice = props.devices[currentVideoIndex.value];

  if (!currentDevice?.streamKey || !videoRef.value) {
    console.error("无效的视频流地址或视频元素未就绪");
    return;
  }

  try {
    // 确保之前的播放器已经销毁
    destroyPlayer();

    flvPlayer = flvjs.createPlayer({
      type: "flv",
      url: currentDevice.streamKey,
      isLive: true,
      hasAudio: false,
      hasVideo: true
    });

    flvPlayer.attachMediaElement(videoRef.value);
    flvPlayer.load();
    // 添加延时确保加载完成
    setTimeout(() => {
      if (flvPlayer) {
        (flvPlayer.play() as Promise<void>).catch(err => {
          console.error("视频播放失败:", err);
        });
      }
    }, 100);
  } catch (error) {
    console.error("初始化播放器失败:", error);
  }
};

// 销毁播放器
const destroyPlayer = () => {
  if (flvPlayer) {
    try {
      flvPlayer.pause();
      flvPlayer.unload();
      flvPlayer.detachMediaElement();
      flvPlayer.destroy();
    } catch (error) {
      console.error("销毁播放器失败:", error);
    } finally {
      flvPlayer = null;
    }
  }
};

// 处理对话框关闭
const handleClose = () => {
  // 先销毁播放器
  destroyPlayer();
  emit("update:visible", false);
};

// 切换最小化状态
const toggleMinimize = async () => {
  // 先销毁播放器
  destroyPlayer();
  isMinimized.value = !isMinimized.value;
  await nextTick();
  initPlayer();
};

onMounted(() => {
  if (props.visible) {
    initPlayer();
  }
});

onBeforeUnmount(() => {
  destroyPlayer();
});

// 监听对话框显示状态
watch(
  () => props.visible,
  async val => {
    dialogVisible.value = val;
    if (val) {
      // 对话框显示时初始化播放器
      await nextTick();
      initPlayer();
    } else {
      // 对话框关闭时销毁播放器
      destroyPlayer();
    }
  }
);

// 监听当前视频索引变化
watch(currentVideoIndex, () => {
  destroyPlayer();
  initPlayer();
});
</script>

<style>
.large-screen-alarm-video-dialog {
  position: relative;
  width: 70% !important;
  overflow: hidden;
  background: #181c2fcc !important;
  border: 1px solid rgb(64 158 255 / 20%);
  border-radius: 8px;

  .call-controls {
    right: 125px;
    bottom: 140px;
    transform: scale(2);
  }

  .el-dialog {
    background: rgb(0 21 41 / 80%);
    backdrop-filter: blur(12px);
    border: 1px solid rgb(0 234 255 / 30%);
    box-shadow: 0 0 20px rgb(0 234 255 / 20%);

    &__header {
      padding: 0;
      margin: 0;
      border-bottom: 1px solid rgb(0 234 255 / 10%);
    }

    &__body {
      padding: 0;
      background: rgb(0 0 0 / 30%);
    }
  }
}
</style>
<style lang="scss" scoped>
.dialog-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px 12px;
  background: rgb(0 21 41 / 60%);

  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    content: "";
    background: linear-gradient(
      90deg,
      transparent,
      rgb(0 234 255 / 30%),
      transparent
    );
  }

  .title {
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 16px; // 对话框标题文字大小
    color: #fff;
    text-shadow: 0 0 10px rgb(0 234 255 / 50%);

    .el-icon {
      color: #00eaff;
    }
  }
}

.video-tabs {
  display: flex;
  justify-content: center;
  margin: 0 20px;

  :deep(.el-radio-group) {
    .el-radio-button__inner {
      color: #fff;
      background: rgb(0 21 41 / 40%);
      border-color: rgb(0 234 255 / 30%);
      transition: all 0.3s;

      &:hover {
        background: rgb(0 234 255 / 10%);
        border-color: rgb(0 234 255 / 50%);
      }
    }

    .el-radio-button__original-radio:checked + .el-radio-button__inner {
      color: #00eaff;
      text-shadow: 0 0 10px rgb(0 234 255 / 50%);
      background: rgb(0 234 255 / 20%);
      border-color: #00eaff;
      box-shadow: -1px 0 0 0 #00eaff;
    }
  }
}

.dialog-controls {
  display: flex;
  gap: 8px;

  :deep(.el-button) {
    padding: 8px;
    color: rgb(255 255 255 / 80%);
    background: transparent;
    border: none;
    transition: all 0.3s;

    &:hover {
      color: #00eaff;
      background: rgb(0 234 255 / 10%);
      transform: scale(1.1);
    }

    .el-icon {
      font-size: 18px;
    }
  }
}

.video-container {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  overflow: hidden;
  background: #000;

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 1px;
    content: "";
    background: linear-gradient(
      90deg,
      transparent,
      rgb(0 234 255 / 30%),
      transparent
    );
  }
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.minimized-video {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 2000;
  width: 320px;
  aspect-ratio: 16/9;
  overflow: hidden;
  cursor: pointer;
  background: rgb(0 21 41 / 80%);
  backdrop-filter: blur(12px);
  border: 1px solid rgb(0 234 255 / 30%);
  border-radius: 8px;
  box-shadow: 0 0 20px rgb(0 234 255 / 20%);
  transition:
    transform 0.3s,
    box-shadow 0.3s;

  &:hover {
    box-shadow: 0 0 30px rgb(0 234 255 / 30%);
    transform: scale(1.02);
  }
}

.minimized-controls {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  color: #fff;
  background: rgb(0 21 41 / 80%);
  border-bottom: 1px solid rgb(0 234 255 / 10%);

  span {
    font-size: 14px; // 最小化窗口标题文字大小
    text-shadow: 0 0 10px rgb(0 234 255 / 50%);
  }

  :deep(.el-button) {
    padding: 4px;
    color: rgb(255 255 255 / 80%);
    background: transparent;
    border: none;
    transition: all 0.3s;

    &:hover {
      color: #00eaff;
      transform: scale(1.1);
    }
  }
}
</style>
