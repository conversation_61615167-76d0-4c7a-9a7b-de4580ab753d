<template>
  <!-- 违法概览弹窗 -->
  <div class="dialog-container" @click.self="handleClose">
    <div class="dialog-content">
      <dv-border-box-12 class="dialog-border">
        <!-- 标题栏 -->
        <div class="dialog-header">
          <div class="title">
            <dv-decoration-5 style="width: 60px; height: 30px" />
            <span class="title-text">违法趋势分析</span>
          </div>
          <div class="close-btn" @click="handleClose">
            <el-icon><Close /></el-icon>
          </div>
        </div>

        <!-- 控制栏 -->
        <div class="control-header">
          <!-- 左侧节点树选择器 -->
          <div class="dialog-area-selector">
            <AreaCascader
              ref="areaCascaderRef"
              v-model="selectedArea"
              :show-home-button="true"
              @change="handleAreaChange"
            />
          </div>

          <!-- 中间Tab选项 -->
          <div class="header-center">
            <div class="tab-group">
              <div
                class="tab-item"
                :class="{ active: activeTab === 'period' }"
                @click="changeChartTypeHandle('period')"
              >
                时段统计
              </div>
              <div
                class="tab-item"
                :class="{ active: activeTab === 'hourly' }"
                @click="changeChartTypeHandle('hourly')"
              >
                分时统计
              </div>
            </div>
          </div>

          <!-- 右侧时间选择器 -->
          <div class="header-right">
            <TimeRangeSelector
              v-model="selectedView"
              :enabled-ranges="['day', 'month', 'year']"
              :custom-labels="{ day: '日', month: '月', year: '年' }"
              :disable-future="true"
              :initial-date="selectedDate"
              :initial-month="selectedMonth"
              :initial-year="selectedYear"
              @change="handleTimeChange"
              @date-change="handleDateChange"
              @month-change="handleMonthChange"
              @year-change="handleYearChange"
            />
          </div>
        </div>

        <!-- 内容区 -->
        <div class="dialog-body">
          <!-- 时段统计内容 -->
          <div v-if="activeTab === 'period'" class="trend-chart-wrapper">
            <div ref="chartRef" class="trend-chart" />
          </div>

          <!-- 分时统计内容 -->
          <div v-else class="hourly-stats-wrapper">
            <PointTrend
              :timeRange="selectedView"
              :selectedArea="selectedArea"
              :selectedDate="selectedDate"
              :selectedMonth="selectedMonth"
              :selectedYear="selectedYear"
            />
          </div>
        </div>

        <!-- 底部装饰 -->
        <dv-decoration-3
          class="dialog-footer"
          style="width: 100%; height: 4px"
        />
      </dv-border-box-12>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getIllegalTrendByDay,
  getIllegalTrendByMonth,
  getIllegalTrendByWeek,
  getIllegalTrendByYear
} from "@/api/screen";
import { useAppStoreHook } from "@/store/modules/app";
import { useUserStoreHook } from "@/store/modules/user";
import { Close } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import * as echarts from "echarts";
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";
import PointTrend from "./PointTrend.vue";
import AreaCascader from "@/components/AreaCascader.vue";
import TimeRangeSelector, {
  type TimeRangeType
} from "@/components/TimeRangeSelector.vue";
import { getNodePathById } from "@/utils/tree";
import { TreeNode } from "@/views/system/system-user/utils/types";

const props = withDefaults(
  defineProps<{
    visible: boolean;
    // 大屏传递的初始时间值
    initialMode?: "day" | "month" | "year";
    initialDate?: string;
    initialMonth?: string;
    initialYear?: string;
  }>(),
  {
    visible: true
  }
);

const emit = defineEmits<{
  (e: "close"): void;
}>();

// 添加 activeTab 状态
const activeTab = ref("period");

// 时间范围选项由 TimeRangeSelector 组件处理

const selectedView = ref<TimeRangeType>(props.initialMode || "day");
const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;
const appStore = useAppStoreHook();
const userStore = useUserStoreHook();

// 新增：节点树选择相关
const selectedArea = ref<number[]>([]);
const cascaderAreaRef = ref();

// 新增：时间选择器相关 - 使用props传递的初始值或默认值
const selectedDate = ref(props.initialDate || dayjs().format("YYYY-MM-DD"));
const selectedMonth = ref(props.initialMonth || dayjs().format("YYYY-MM"));
const selectedYear = ref(
  props.initialYear ||
    (props.initialMonth
      ? props.initialMonth.split("-")[0]
      : dayjs().format("YYYY"))
);

const trendData = ref<{
  series: { data: number[]; name: string }[];
  categories: string[];
}>({
  series: [],
  categories: []
});

// 添加违法类型颜色映射
const violationColors = {
  超员: "#FF0000", // 红色
  未佩戴头盔: "#FFA500", // 橙色
  加装遮阳伞: "#FFFF00", // 黄色
  "超员+未佩戴头盔": "#00FF00", // 绿色
  "超员+加装遮阳伞": "#00FFFF", // 青色
  "未佩戴头盔+加装遮阳伞": "#0000FF", // 蓝色
  "超员+未佩戴头盔+加装遮阳伞": "#800080" // 紫色
};

// 获取地址参数
const getAddressParams = (areaIds: number[]) => {
  const params = {
    city: userStore.userInfo.city
  } as any;

  if (!areaIds?.length) return params;

  let currentNode = userStore.userRegionTree.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return params;

  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }

  return params;
};

// 根据ID查找节点
const findNodeById = (nodes: TreeNode[], id: number): TreeNode | undefined => {
  if (!nodes || !id) return undefined;

  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.childList) {
      const found = findNodeById(node.childList, id);
      if (found) return found;
    }
  }
  return undefined;
};

// 获取趋势数据
const fetchTrendData = async (view: TimeRangeType = selectedView.value) => {
  try {
    let response;

    // 构建地址参数：使用节点树选择的地址或默认大屏区域
    const addressParams = selectedArea.value?.length
      ? getAddressParams(selectedArea.value)
      : appStore.getLargeScreenArea;

    switch (view) {
      case "day":
        // 使用用户选择的日期和节点
        response = await getIllegalTrendByDay(
          selectedDate.value,
          addressParams
        );
        break;
      case "month":
        // 使用用户选择的月份和节点
        const selectedMonthDate = dayjs(selectedMonth.value);
        response = await getIllegalTrendByMonth(
          selectedMonthDate.year(),
          selectedMonthDate.month() + 1,
          addressParams
        );
        break;
      case "year":
        // 使用用户选择的年份和节点
        const selectedYearDate = dayjs(selectedYear.value);
        response = await getIllegalTrendByYear(
          selectedYearDate.year(),
          addressParams
        );
        if (response.code === 200 && response.data.categories) {
          response.data.categories = response.data.categories.map(
            (month: string) => {
              const monthNum = parseInt(month);
              return monthNum.toString().padStart(2, "0");
            }
          );
        }
        break;
      default:
        response = await getIllegalTrendByDay(
          selectedDate.value,
          addressParams
        );
    }

    if (response.code === 200) {
      trendData.value = response.data;
      nextTick(() => {
        renderChart(view);
      });
    }
  } catch (error) {
    console.error("Failed to fetch trend data:", error);
  }
};

const renderChart = (currentView?: string) => {
  if (!chartRef.value || !trendData.value.categories.length) return;
  if (chart) {
    chart.dispose();
  }
  chart = echarts.init(chartRef.value);

  // 获取所有违法类型名称
  const allViolationTypes = Object.keys(violationColors);

  // 添加数据过滤逻辑
  const filterDataByTimeRange = (
    categories: string[],
    seriesData: { data: number[]; name: string }[],
    currentView: string = selectedView.value
  ) => {
    const now = new Date();
    let filteredIndices: number[] = [];

    if (currentView === "day") {
      // 今日：过滤到当前小时
      const currentHour = now.getHours();
      filteredIndices = categories
        .map((_, index) => index)
        .filter(index => {
          const timeStr = categories[index];
          let itemHour = 0;

          if (typeof timeStr === "string") {
            if (timeStr.includes(":")) {
              const [hourStr] = timeStr.split(":");
              itemHour = parseInt(hourStr) || 0;
            } else if (timeStr.match(/^\d+$/)) {
              itemHour = parseInt(timeStr) || 0;
            } else if (timeStr.includes("时")) {
              const match = timeStr.match(/(\d+)时/);
              itemHour = match ? parseInt(match[1]) : 0;
            }
          }

          return itemHour <= currentHour;
        });
    } else if (currentView === "month") {
      // 本月：过滤到当前日期
      const currentDate = now.getDate();

      filteredIndices = categories
        .map((_, index) => index)
        .filter(index => {
          const dateStr = categories[index];
          let itemDay = 0;

          if (typeof dateStr === "string") {
            if (dateStr.includes("-")) {
              const parts = dateStr.split("-");
              if (parts.length >= 2) {
                itemDay = parseInt(parts[parts.length - 1]) || 0;
              }
            } else if (dateStr.match(/^\d+$/)) {
              itemDay = parseInt(dateStr) || 0;
            } else if (dateStr.includes("号")) {
              const match = dateStr.match(/(\d+)号/);
              itemDay = match ? parseInt(match[1]) : 0;
            }
          }

          const shouldInclude = itemDay <= currentDate;
          return shouldInclude;
        });
    } else if (currentView === "year") {
      // 全年：过滤到当前月份
      const currentMonth = now.getMonth() + 1;
      filteredIndices = categories
        .map((_, index) => index)
        .filter(index => {
          const monthStr = categories[index];
          let itemMonth = 0;

          if (typeof monthStr === "string") {
            if (monthStr.includes("-")) {
              const parts = monthStr.split("-");
              itemMonth = parseInt(parts[parts.length - 1]) || 0;
            } else if (monthStr.match(/^\d+$/)) {
              itemMonth = parseInt(monthStr) || 0;
            } else if (monthStr.includes("月")) {
              const match = monthStr.match(/(\d+)月/);
              itemMonth = match ? parseInt(match[1]) : 0;
            }
          }

          return itemMonth <= currentMonth;
        });
    } else {
      // 本周等其他范围：显示所有数据
      filteredIndices = categories.map((_, index) => index);
    }

    // 过滤categories和series数据
    const filteredCategories = filteredIndices.map(i => categories[i]);
    const filteredSeries = seriesData.map(series => ({
      ...series,
      data: filteredIndices.map(i => series.data[i] || 0)
    }));

    return { filteredCategories, filteredSeries };
  };

  const { filteredCategories, filteredSeries } = filterDataByTimeRange(
    trendData.value.categories,
    trendData.value.series,
    currentView
  );

  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 10, 30, 0.85)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        fontSize: 22, // ECharts悬停提示文字字体大小
        color: "rgba(255, 255, 255, 0.9)"
      }
    },
    legend: {
      data: allViolationTypes,
      textStyle: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // ECharts图例文字字体大小（违法类型图例）
      },
      top: 10,
      left: "center",
      orient: "horizontal",
      itemGap: 20, // 图例项之间的间距
      itemWidth: 14, // 图例标记的宽度
      itemHeight: 14 // 图例标记的高度
    },
    grid: {
      top: 80, // 增加顶部间距，为图例留出空间
      left: "5%", // 增加左侧间距
      right: "5%", // 增加右侧间距
      bottom: "8%", // 增加底部间距
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: filteredCategories,
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)"
        }
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // ECharts X轴标签字体大小 14px（时间刻度标签）
      }
    },
    yAxis: {
      type: "value",
      name: "违法数量(起)",
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // ECharts Y轴标题字体大小 14px（违法数量(起)）
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)"
        }
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // ECharts Y轴标签字体大小 14px（数值刻度标签）
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)"
        }
      }
    },
    series: allViolationTypes.map(type => {
      // 查找该类型是否有数据
      const seriesData = filteredSeries.find(s => s.name === type);

      return {
        name: type,
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 8,
        // 如果没有数据，使用空数组
        data: seriesData?.data || Array(filteredCategories.length).fill(0),
        lineStyle: {
          color: violationColors[type],
          width: 2
        },
        itemStyle: {
          color: violationColors[type],
          borderWidth: 2,
          borderColor: "#fff"
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: `${violationColors[type]}99` // 60% 透明度
            },
            {
              offset: 1,
              color: `${violationColors[type]}1A` // 10% 透明度
            }
          ])
        },
        emphasis: {
          focus: "series",
          itemStyle: {
            borderWidth: 3
          }
        }
      };
    })
  };

  chart.setOption(option);
};

// 监听弹窗显示状态
onMounted(() => {
  // 初始化默认选中区域
  selectedArea.value = appStore.getLargeScreenPathId;

  if (chartRef.value) {
    if (chart) {
      chart.dispose();
      chart = null;
    }
    chart = echarts.init(chartRef.value);
    fetchTrendData();
  }
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener("resize", handleResize);
});

const handleClose = () => {
  emit("close");
};

const changeChartTypeHandle = type => {
  activeTab.value = type;
  if (type === "period") {
    fetchTrendData();
  }
};

const handleTimeChange = (value: TimeRangeType) => {
  selectedView.value = value;
  if (activeTab.value === "period") {
    fetchTrendData(value);
  }
};

// 新增：地区选择相关方法
const handleAreaChange = (value: number[]) => {
  selectedArea.value = value;
  // 重新获取数据（appStore的区域会通过其他机制更新）
  fetchTrendData(selectedView.value);
};

const areaNodeClickHandle = (item: any) => {
  const areaPath = getNodePathById(item.id, userStore.getUserRegionTree);
  const ids = areaPath.map(node => node.id);
  selectedArea.value = ids;
  handleAreaChange(ids);
  if (cascaderAreaRef.value) {
    cascaderAreaRef.value.togglePopperVisible(false);
  }
};

// 新增：时间选择器相关方法
const handleDateChange = (date: string) => {
  selectedDate.value = date;
  fetchTrendData(selectedView.value);
};

const handleMonthChange = (month: string) => {
  selectedMonth.value = month;
  fetchTrendData(selectedView.value);
};

const handleYearChange = (year: string) => {
  selectedYear.value = year;
  fetchTrendData(selectedView.value);
};

// 监听props变化，同步更新内部状态
watch(
  () => props.initialMode,
  newMode => {
    if (newMode && newMode !== selectedView.value) {
      selectedView.value = newMode;
    }
  },
  { immediate: true }
);

watch(
  () => props.initialDate,
  newDate => {
    if (newDate && newDate !== selectedDate.value) {
      selectedDate.value = newDate;
    }
  },
  { immediate: true }
);

watch(
  () => props.initialMonth,
  newMonth => {
    if (newMonth && newMonth !== selectedMonth.value) {
      selectedMonth.value = newMonth;
      selectedYear.value = newMonth.split("-")[0];
    }
  },
  { immediate: true }
);

watch(
  () => props.initialYear,
  newYear => {
    if (newYear && newYear !== selectedYear.value) {
      selectedYear.value = newYear;
    }
  },
  { immediate: true }
);

const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

// 监听窗口大小变化
window.addEventListener("resize", handleResize);
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgb(0 10 30 / 80%);
  backdrop-filter: blur(8px);
}

.dialog-content {
  position: absolute; // 更爱为绝对定位，保证居中
  left: 50%; // 居中定位
  top: 50%; // 剧中定位
  transform: translate(-50%, -50%) scale(var(--scale-ratio)); // 剧中定位，保持大屏页面相同缩放比例
  width: 1792px; // 根据需求调整宽度，设计定稿宽度2560px，根据弹窗大小调整宽度，小于2560
  height: 1024px; // 根据需求调整宽度，设计定稿宽度1271px，根据弹窗大小调整宽度，小于1271
  // animation: zoomIn 0.3s ease-out; // 取消窗口动画
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 30px;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  .title {
    display: flex;
    gap: 12px;
    align-items: center;

    .title-text {
      @extend .fs-dialog-title; // 弹窗标题文字字体大小
      font-weight: 500;
      color: #fff;
      text-shadow: 0 0 10px rgb(64 158 255 / 50%);
    }
  }

  .close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: rgb(255 255 255 / 10%);
    border-radius: 50%;
    transition: all 0.3s;

    &:hover {
      background: rgb(255 255 255 / 20%);
      transform: rotate(90deg);
    }

    .el-icon {
      @extend .fs-illegal-close; // 弹窗关闭按钮图标字体大小
      color: rgb(255 255 255 / 80%);
    }
  }
}

/* 控制栏样式 */
.control-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid rgb(64 158 255 / 20%);

  .dialog-area-selector,
  .header-center,
  .header-right {
    display: flex;
    align-items: center;
  }

  .header-center {
    .tab-group {
      display: flex;
      align-items: center;
      height: 42px;
      gap: 2px;
      padding: 2px;
      background: rgb(0 24 75 / 30%);
      border: 1px solid rgb(64 158 255 / 20%);
      border-radius: 4px;

      .tab-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 36px !important;
        padding: 0 16px;
        @extend .fs-illegal-tab; // Tab选项卡文字字体大小 24px（时段统计/分时统计）
        line-height: 36px !important;
        color: rgb(255 255 255 / 70%);
        cursor: pointer;
        border-radius: 2px;
        transition: all 0.3s;

        &.active {
          color: #fff;
          background: #409eff;
          @extend .fs-illegal-tab; // Tab选项卡激活状态文字字体大小 24px
          line-height: 36px !important;
        }

        &:hover:not(.active) {
          color: #fff;
        }
      }
    }
  }
}

.dialog-body {
  height: calc(100% - 120px); /* 调整高度以适应新增的tab */
  padding: 20px;
}

.trend-chart-wrapper {
  height: 100%;
  padding: 20px;
  background: rgb(0 24 75 / 30%);
  border: 1px solid rgb(64 158 255 / 20%);
  border-radius: 4px;

  .trend-chart {
    width: 100%;
    height: 100%;
  }
}

/* 添加分时统计样式 */
.hourly-stats-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  background: rgb(0 24 75 / 30%);
  border: 1px solid rgb(64 158 255 / 20%);
  border-radius: 4px;
}
</style>
