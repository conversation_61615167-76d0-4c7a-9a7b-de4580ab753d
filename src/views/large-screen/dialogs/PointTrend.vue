<template>
  <!-- 分时统计组件 -->
  <div class="point-trend">
    <div class="header">
<!--      <div v-if="trendData" class="summary">-->
<!--        <div class="summary-item row">-->
<!--          <div class="label">总违法数</div>-->
<!--          <div class="value">{{ getTotalViolations() }}</div>-->
<!--        </div>-->
<!--      </div>-->
    </div>
    <div ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup lang="ts">
import { useUserStoreHook } from "@/store/modules/user";
import { useAppStoreHook } from "@/store/modules/app";
import { http } from "@/utils/http";
import type { EChartsOption, LineSeriesOption } from "echarts";
import * as echarts from "echarts";
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";
import { TreeNode } from "@/views/system/system-user/utils/types";

const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;

const props = defineProps<{
  timeRange: string;
  selectedArea?: number[];
  selectedDate?: string;
  selectedMonth?: string;
  selectedYear?: string;
}>();

// 定义数据接口
interface HourlyStat {
  hour: string;
  totalCount: number;
  persuasionRate: string;
  persuadedCount: number;
  effectiveCount: number;
  details: Detail[];
}

interface Detail {
  type: string;
  totalCount: number;
  color: string;
}

interface TrendData {
  yearMonth: string;
  hourlyStats: HourlyStat[];
}

// 定义响应式数据
const trendData = ref<TrendData>();
const userStore = useUserStoreHook();
const appStore = useAppStoreHook();

// 获取地址参数
const getAddressParams = (areaIds: number[]) => {
  const params = {
    city: userStore.userInfo.city
  } as any;

  if (!areaIds?.length) return params;

  let currentNode = userStore.userRegionTree.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return params;

  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }

  return params;
};

// 根据ID查找节点
const findNodeById = (nodes: TreeNode[], id: number): TreeNode | undefined => {
  if (!nodes || !id) return undefined;

  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.childList) {
      const found = findNodeById(node.childList, id);
      if (found) return found;
    }
  }
  return undefined;
};

// 监听参数发生改变
watch(
  [
    () => props.timeRange,
    () => props.selectedArea,
    () => props.selectedDate,
    () => props.selectedMonth,
    () => props.selectedYear
  ],
  () => fetchTrendData()
);

// 获取趋势数据
const fetchTrendData = async () => {
  let startTime: Date;
  let endTime: Date;

  // 根据时间范围设置起止时间
  switch (props.timeRange) {
    case "day":
      // 使用用户选择的日期
      const selectedDate = props.selectedDate
        ? new Date(props.selectedDate)
        : new Date();
      startTime = new Date(selectedDate.setHours(0, 0, 0, 0));
      endTime = new Date(selectedDate.setHours(23, 59, 59, 999));
      break;
    case "month":
      // 使用用户选择的月份
      const selectedMonth = props.selectedMonth
        ? new Date(props.selectedMonth + "-01")
        : new Date();
      const year = selectedMonth.getFullYear();
      const month = selectedMonth.getMonth();
      startTime = new Date(year, month, 1); // 选择月份的1号
      endTime = new Date(year, month + 1, 0); // 选择月份的最后一天
      startTime.setHours(0, 0, 0, 0);
      endTime.setHours(23, 59, 59, 999);
      break;
    case "year":
      // 使用用户选择的年份
      const selectedYear = props.selectedYear
        ? new Date(props.selectedYear)
        : new Date();
      const yearNum = selectedYear.getFullYear();
      startTime = new Date(yearNum, 0, 1); // 选择年份的1月1号
      endTime = new Date(yearNum, 11, 31); // 选择年份的12月31号
      startTime.setHours(0, 0, 0, 0);
      endTime.setHours(23, 59, 59, 999);
      break;
    default:
      const defaultDate = props.selectedDate
        ? new Date(props.selectedDate)
        : new Date();
      startTime = new Date(defaultDate.setHours(0, 0, 0, 0));
      endTime = new Date(defaultDate.setHours(23, 59, 59, 999));
  }

  // 格式化日期时间
  const formatDateTime = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  // 构建地址参数：使用节点树选择的地址或默认地址
  const addressParams = props.selectedArea?.length
    ? getAddressParams(props.selectedArea)
    : appStore.largeScreenArea;

  const params = {
    startTime: formatDateTime(startTime),
    endTime: formatDateTime(endTime),
    ...addressParams
  };

  const res = await http.request<any>(
    "get",
    "/largeScreen/getHourlyViolationStats",
    {
      params
    }
  );

  if (res.code === 200) {
    trendData.value = res.data;
    nextTick(() => {
      renderChart();
    });
  }
};

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || !trendData.value) return;
  if (chart) {
    chart.dispose();
  }
  chart = echarts.init(chartRef.value);

  // 添加数据过滤逻辑 - 分时统计只在今日时过滤
  const filterDataByTimeRange = (stats: HourlyStat[]) => {
    const now = new Date();

    if (props.timeRange === "day") {
      // 今日：过滤到当前小时
      const currentHour = now.getHours();
      return stats.filter(stat => {
        const hour = parseInt(stat.hour) || 0;
        return hour <= currentHour;
      });
    }

    // 分时统计：本月、全年、本周都保持原有显示逻辑
    return stats;
  };

  const filteredStats = filterDataByTimeRange(trendData.value.hourlyStats);

  // 处理过滤后的数据
  const hours = filteredStats.map(stat => stat.hour);
  const totalCounts = filteredStats.map(stat => stat.totalCount);

  // 收集所有违法类型
  const typeData: Record<string, { data: number[]; color: string }> = {};

  // 初始化数据结构
  filteredStats.forEach(stat => {
    stat.details.forEach(detail => {
      if (!typeData[detail.type]) {
        typeData[detail.type] = {
          data: new Array(filteredStats.length).fill(0),
          color: detail.color // 保存后端返回的颜色
        };
      }
    });
  });

  // 填充数据
  filteredStats.forEach((stat, index) => {
    stat.details.forEach(detail => {
      typeData[detail.type].data[index] = detail.totalCount;
    });
  });

  // 定义固定的颜色
  const lineColors = {
    totalViolations: "#F56C6C", // 违法总数 - 红色
    overload: "#E040FB", // 超员 - 紫红色
    noHelmet: "#FF9800", // 未佩戴头盔 - 橙色
    umbrella: "#00BCD4", // 加装遮阳伞 - 青色
    overloadNoHelmet: "#9C27B0", // 超员+未佩戴头盔 - 深紫色
    overloadUmbrella: "#3F51B5", // 超员+加装遮阳伞 - 靛蓝色
    noHelmetUmbrella: "#009688", // 未佩戴头盔+加装遮阳伞 - 青绿色
    all: "#795548" // 超员+未佩戴头盔+加装遮阳伞 - 棕色
  };

  // 获取违法类型对应的颜色
  const getViolationTypeColor = (type: string) => {
    const colorMap = {
      违法总数: lineColors.totalViolations,
      超员: lineColors.overload,
      未佩戴头盔: lineColors.noHelmet,
      加装遮阳伞: lineColors.umbrella,
      "超员+未佩戴头盔": lineColors.overloadNoHelmet,
      "超员+加装遮阳伞": lineColors.overloadUmbrella,
      "未佩戴头盔+加装遮阳伞": lineColors.noHelmetUmbrella,
      "超员+未佩戴头盔+加装遮阳伞": lineColors.all
    };
    return (
      colorMap[type] || `#${Math.floor(Math.random() * 16777215).toString(16)}`
    );
  };

  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "line",
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)",
          width: 1,
          type: "solid"
        }
      },
      backgroundColor: "rgba(0, 24, 75, 0.8)",
      borderColor: "rgba(64, 158, 255, 0.3)",
      borderWidth: 1,
      padding: [6, 8], // 减小内边距，让tooltip更紧凑
      textStyle: {
        color: "rgba(255, 255, 255, 0.8)",
        fontSize: 12 // 减小字体大小，让tooltip更小巧
      },
      // 优化tooltip位置，防止被遮挡
      confine: true, // 限制tooltip在图表容器内
      appendToBody: true, // 将tooltip添加到body，避免被对话框容器裁剪
      position: function (point, params, dom, rect, size) {
        // 智能位置计算，防止tooltip超出边界
        const [mouseX, mouseY] = point;
        const { contentSize, viewSize } = size;
        const [tooltipWidth, tooltipHeight] = contentSize;
        const [containerWidth, containerHeight] = viewSize;

        let x = mouseX + 10; // 默认在鼠标右侧
        let y = mouseY - tooltipHeight / 2; // 垂直居中

        // 检查右边界，如果超出则显示在左侧
        if (x + tooltipWidth > containerWidth) {
          x = mouseX - tooltipWidth - 10;
        }

        // 检查上下边界
        if (y < 0) {
          y = 10;
        } else if (y + tooltipHeight > containerHeight) {
          y = containerHeight - tooltipHeight - 10;
        }

        return [x, y];
      },
      formatter: (params: any) => {
        let result = `<div style="color: #fff; font-weight: 500; margin-bottom: 4px;">
          ${params[0].name}
        </div>`;
        params.forEach((param: any) => {
          const value = param.value === undefined ? "-" : param.value;
          const marker = `<span style="display: inline-block;
            width: 8px;
            height: 8px;
            background-color: ${param.color};
            border-radius: 50%;
            margin-right: 6px;"></span>`;

          result += `<div style="display: flex; align-items: center; margin: 4px 0;">
            ${marker}${param.seriesName}：<span style="color: rgba(255, 255, 255, 0.8); margin-left: 4px;">${value}</span>
          </div>`;
        });
        return result;
      }
    },
    legend: {
      data: [
        "违法总数",
        "超员",
        "未佩戴头盔",
        "加装遮阳伞",
        "超员+未佩戴头盔",
        "超员+加装遮阳伞",
        "未佩戴头盔+加装遮阳伞",
        "超员+未佩戴头盔+加装遮阳伞",
        ...Object.keys(typeData).filter(
          type =>
            ![
              "超员",
              "未佩戴头盔",
              "加装遮阳伞",
              "超员+未佩戴头盔",
              "超员+加装遮阳伞",
              "未佩戴头盔+加装遮阳伞",
              "超员+未佩戴头盔+加装遮阳伞"
            ].includes(type)
        )
      ],
      textStyle: {
        color: "#fff",
        fontSize: 22 // ECharts图例文字字体大小（违法类型图例）
      },
      top: 30,
      type: "scroll",
      width: "80%"
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: hours,
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // ECharts X轴标签字体大小（时间刻度标签）
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)"
        }
      }
    },
    yAxis: {
      type: "value",
      name: "违法数量",
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // ECharts Y轴标题字体大小（违法数量）
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // ECharts Y轴标签字体大小（数值刻度标签）
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)"
        }
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)"
        }
      }
    },
    series: [
      {
        name: "违法总数",
        type: "line",
        data: totalCounts,
        smooth: true,
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          color: lineColors.totalViolations
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: lineColors.totalViolations + "40"
            },
            {
              offset: 1,
              color: lineColors.totalViolations + "00"
            }
          ])
        }
      },
      // 固定顺序的违法类型
      ...[
        "超员",
        "未佩戴头盔",
        "加装遮阳伞",
        "超员+未佩戴头盔",
        "超员+加装遮阳伞",
        "未佩戴头盔+加装遮阳伞",
        "超员+未佩戴头盔+加装遮阳伞"
      ].map(type => {
        const data =
          typeData[type]?.data || new Array(filteredStats.length).fill(0);
        const color = typeData[type]?.color || getViolationTypeColor(type);
        return {
          name: type,
          type: "line" as const,
          data,
          smooth: true,
          symbol: "circle",
          symbolSize: 6,
          lineStyle: {
            width: 2,
            type: "dashed" as const
          },
          itemStyle: {
            color
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: color + "40"
              },
              {
                offset: 1,
                color: color + "00"
              }
            ])
          }
        };
      }),
      // 添加其他未在固定列表中的违法类型
      ...Object.entries(typeData)
        .filter(
          ([type]) =>
            ![
              "超员",
              "未佩戴头盔",
              "加装遮阳伞",
              "超员+未佩戴头盔",
              "超员+加装遮阳伞",
              "未佩戴头盔+加装遮阳伞",
              "超员+未佩戴头盔+加装遮阳伞"
            ].includes(type)
        )
        .map(
          ([type, { data, color }]): LineSeriesOption => ({
            name: type,
            type: "line" as const,
            data,
            smooth: true,
            symbol: "circle",
            symbolSize: 6,
            lineStyle: {
              width: 2,
              type: "dashed" as const
            },
            itemStyle: {
              color:
                color || `#${Math.floor(Math.random() * 16777215).toString(16)}`
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color:
                    (color ||
                      `#${Math.floor(Math.random() * 16777215).toString(16)}`) +
                    "40"
                },
                {
                  offset: 1,
                  color:
                    (color ||
                      `#${Math.floor(Math.random() * 16777215).toString(16)}`) +
                    "00"
                }
              ])
            }
          })
        )
    ]
  };

  chart.setOption(option);
};

// 监听窗口大小变化
const handleResize = () => {
  chart?.resize();
};

onMounted(() => {
  fetchTrendData();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  chart?.dispose();
  window.removeEventListener("resize", handleResize);
});

// 计算总违法数 - 分时统计只在今日时过滤
const getTotalViolations = () => {
  if (!trendData.value) return 0;

  // 应用时间过滤逻辑 - 分时统计只在今日时过滤
  const now = new Date();
  let filteredStats = trendData.value.hourlyStats;

  if (props.timeRange === "day") {
    // 今日：过滤到当前小时
    const currentHour = now.getHours();
    filteredStats = trendData.value.hourlyStats.filter(stat => {
      const hour = parseInt(stat.hour) || 0;
      return hour <= currentHour;
    });
  }
  // 分时统计：本月、全年、本周都保持原有显示逻辑，不进行过滤

  return filteredStats.reduce((sum, stat) => sum + stat.totalCount, 0);
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
.point-trend {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  background: rgb(0 24 75 / 60%);
  border-radius: 4px;

  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;

    .summary {
      display: flex;
      gap: 24px;

      .summary-item {
        position: relative;
        padding: 0 16px;
        text-align: center;

        &:not(:last-child)::after {
          position: absolute;
          top: 50%;
          right: -12px;
          width: 1px;
          height: 24px;
          content: "";
          background: rgb(255 255 255 / 10%);
          transform: translateY(-50%);
        }

        .label {
          margin-bottom: 4px;
          @extend .fs-illegal-tab-value; // 统计标签文字字体大小（总违法数等标签）
          color: rgb(255 255 255 / 70%);
        }

        .value {
          font-family: Orbitron, sans-serif;
          @extend .fs-illegal-tab-value; // 统计数值文字字体大小（数字显示）
          font-weight: 500;
          color: transparent;
          background: linear-gradient(to bottom, #fff, #00eaff);
          background-clip: text;
        }
      }
      // 新增：让 row 类横向排列
      .summary-item.row {
        position: relative;
        display: flex;
        gap: 8px; // 文字和数字间距
        align-items: baseline; // 使用基线对齐，让文字和数字在同一基线上
        justify-content: center;
        padding: 0 16px;
        text-align: center;
      }
    }
  }
}

.chart-container {
  flex: 1;
  width: 100%;
  min-height: 0;
}
</style>
