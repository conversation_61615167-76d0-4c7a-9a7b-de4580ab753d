<template>
  <!-- 上岗情况弹窗 -->
  <div v-if="visible" class="dialog-container" @click.self="handleClose">
    <div class="dialog-content">
      <dv-border-box-12 class="dialog-border">
        <!-- 标题区域 -->
        <div class="header-section">
          <div class="title-area">
            <!-- 表格类型选择器 -->
            <div class="table-type-selector">
              <div class="custom-segmented">
                <button
                  v-for="option in tableTypeOptions"
                  :key="option.value"
                  :class="[
                    'segmented-button',
                    { active: currentTableType === option.value }
                  ]"
                  @click="handleTableTypeClick(option.value)"
                >
                  {{ option.label }}
                </button>
              </div>
            </div>
          </div>
          <div class="controls-area">
            <!-- 区域选择器 -->
            <div class="area-selector-section">
              <AreaCascader
                ref="areaCascaderRef"
                v-model="selectedArea"
                :show-home-button="true"
                @change="handleAreaChange"
              />
            </div>

            <div class="date-selector">
              <CustomDatePicker
                v-model="currentDate"
                :disable-future="true"
                style="width: 160px"
                placement="bottom"
                @change="handleDateChange"
              />
            </div>
            <div class="close-btn" @click="handleClose">
              <el-icon><Close /></el-icon>
            </div>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
          <!-- 表格容器 -->
          <div class="table-container">
            <el-table
              :data="flattenedData"
              :height="tableHeight"
              size="small"
              :span-method="handleSpanMethod"
              :row-class-name="getRowClassName"
              :row-style="getRowStyle"
              :header-cell-style="{
                background: 'rgba(0, 24, 75, 0.7)',
                color: '#fff',
                borderBottom: '2px solid rgba(64, 158, 255, 0.5)',
                padding: '10px 0',
                fontWeight: '600',
                height: '65px'
              }"
              :cell-style="getCellStyle"
            >
              <!-- 站点信息列 - 只有站点列表显示 -->
              <el-table-column
                v-if="currentTableType === 'site'"
                label="劝导站"
                min-width="80"
                align="center"
                header-align="center"
              >
                <template #default="{ row }">
                  <div
                    class="site-info cell-hover-wrapper"
                    @mouseenter="handleSiteHover(row.siteId, true)"
                    @mouseleave="handleSiteHover(row.siteId, false)"
                  >
                    <span>{{ row.hamlet }}</span>
                  </div>
                </template>
              </el-table-column>

              <!-- 劝导站列 - 人员列表显示 -->
              <el-table-column
                v-if="currentTableType !== 'site'"
                label="劝导站"
                min-width="100"
                align="center"
                header-align="center"
              >
                <template #default="{ row }">
                  <div class="cell-hover-wrapper">
                    <span>{{ row.hamlet }}</span>
                  </div>
                </template>
              </el-table-column>

              <!-- 人员姓名 -->
              <el-table-column
                label="姓名"
                min-width="80"
                align="center"
                header-align="center"
              >
                <template #default="{ row }">
                  <div
                    class="cell-hover-wrapper"
                    @mouseenter="() => handleSiteHover(row.siteId, true)"
                    @mouseleave="() => handleSiteHover(row.siteId, false)"
                  >
                    {{ row.name }}
                  </div>
                </template>
              </el-table-column>

              <!-- 职位 -->
              <el-table-column
                label="岗位"
                min-width="50"
                align="center"
                header-align="center"
              >
                <template #default="{ row }">
                  <div
                    class="cell-hover-wrapper"
                    @mouseenter="() => handleSiteHover(row.siteId, true)"
                    @mouseleave="() => handleSiteHover(row.siteId, false)"
                  >
                    {{ row.deptName }}
                  </div>
                </template>
              </el-table-column>

              <!-- 手机号 -->
              <el-table-column
                label="手机号"
                min-width="100"
                align="center"
                header-align="center"
              >
                <template #default="{ row }">
                  <div
                    class="cell-hover-wrapper"
                    @mouseenter="() => handleSiteHover(row.siteId, true)"
                    @mouseleave="() => handleSiteHover(row.siteId, false)"
                  >
                    {{ row.phone }}
                  </div>
                </template>
              </el-table-column>

              <!-- 人员状态 -->
              <el-table-column
                label="当前状态"
                min-width="80"
                align="center"
                header-align="center"
              >
                <template #default="{ row }">
                  <div
                    class="cell-hover-wrapper"
                    @mouseenter="() => handleSiteHover(row.siteId, true)"
                    @mouseleave="() => handleSiteHover(row.siteId, false)"
                  >
                    <span
                      v-if="getStaffStatus(row)"
                      class="status-text"
                      :data-status="getStaffStatus(row)"
                    >
                      {{ getStaffStatus(row) }}
                    </span>
                    <span v-else class="empty-status">-</span>
                  </div>
                </template>
              </el-table-column>

              <!-- 上岗情况 -->
              <el-table-column
                label="上岗情况"
                min-width="250"
                align="center"
                header-align="center"
              >
                <template #default="{ row }">
                  <div
                    v-if="isSiteFirstRow(row)"
                    class="leave-info"
                    @mouseenter="() => handleSiteHover(row.siteId, true)"
                    @mouseleave="() => handleSiteHover(row.siteId, false)"
                  >
                    <div v-if="isDialogReady" class="timeline-section">
                      <TimeLine
                        :key="currentDate + '-' + row.siteId"
                        :shifts="getSiteShifts(row.siteId)"
                        :device-fault-records="
                          getSiteDeviceFaultRecords(row.siteId)
                        "
                        :view-date="currentDate"
                        :freeze-data="true"
                        timeOfDay="morning"
                      />
                    </div>
                    <div v-else class="timeline-loading">
                      <span>加载中...</span>
                    </div>

                    <div v-if="isDialogReady" class="timeline-section">
                      <TimeLine
                        :key="currentDate + '-' + row.siteId + '-afternoon'"
                        :shifts="getSiteShifts(row.siteId)"
                        :device-fault-records="
                          getSiteDeviceFaultRecords(row.siteId)
                        "
                        :view-date="currentDate"
                        :freeze-data="true"
                        timeOfDay="afternoon"
                      />
                    </div>
                    <div v-else class="timeline-loading">
                      <span>加载中...</span>
                    </div>
                  </div>
                  <div
                    v-else
                    class="empty-cell-hover"
                    @mouseenter="() => handleSiteHover(row.siteId, true)"
                    @mouseleave="() => handleSiteHover(row.siteId, false)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页区域 -->
          <div class="pagination-section">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
              background
              popper-class="attendance-detail-page-select"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </dv-border-box-12>
    </div>
  </div>
</template>

<script setup lang="ts" isolate>
import { Close } from "@element-plus/icons-vue";
import {
  computed,
  ref,
  watch,
  defineComponent,
  defineOptions,
  nextTick
} from "vue";
import TimeLine from "./TimeLine.vue";
import { getAttendance } from "@/api/largeScreen";
import { useAppStoreHook } from "@/store/modules/app";
import { useUserStoreHook } from "@/store/modules/user";
import CustomDatePicker from "@/components/CustomDatePicker.vue";
import { getNodePathById } from "@/utils/tree";
import AreaCascader from "@/components/AreaCascader.vue";

interface AbnormalRecord {
  type: string;
  startTime: string;
  endTime: string;
  duration: string;
  description: string;
}

interface Shift {
  shiftName: string;
  startTime: string;
  endTime: string;
  status: string;
  abnormalRecords?: AbnormalRecord[];
  staffCount?: number;
}

interface Staff {
  userId: number;
  name: string; // 已转换的userName
  phone: string;
  deptName: string;
  hasAbnormal: boolean;
  status: string; // 新增：整体状态
  shifts: Array<{
    scheduleId: number; // 已生成的唯一ID
    shiftName: string;
    startTime: string;
    endTime: string;
    status: string;
    statusText: string;
    leavePostDetails?: Array<{
      startTime: string;
      endTime: string;
      duration: number;
      status: string;
    }>;
  }>;
}

interface Props {
  visible: boolean;
  title: string;
  tableData: Array<{
    site: string;
    city: string;
    county: string;
    township: string;
    hamlet: string;
    hasAbnormal: boolean;
    totalStaff: number;
    siteId: string;
    allShifts?: any[];
    deviceFaultRecords?: Array<{
      equipmentNumber: string;
      ip: string;
      startTime: string;
      endTime: string;
      duration: string;
      description: string;
    }>;
    staff: Staff[];
  }>;
  defaultDate?: string;
  defaultTableType?: string; // 新增：默认显示的表格类型
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: "站点详情",
  tableData: () => [],
  defaultDate: () => new Date().toISOString().split("T")[0],
  defaultTableType: "site"
});

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const appStore = useAppStoreHook();
const userStore = useUserStoreHook();

// 当前选择的日期，初始化为 props.defaultDate
const currentDate = ref(props.defaultDate);

// 区域选择相关
const areaCascaderRef = ref();
const selectedArea = ref<number[]>([]);

// 表格类型选项
const tableTypeOptions = [
  { label: "全部列表", value: "site" },
  { label: "在岗列表", value: "on-duty" },
  { label: "脱岗列表", value: "off-duty" },
  { label: "休息列表", value: "rest" },
  { label: "故障列表", value: "fault" }
];

// 新增：表格类型管理 - 直接使用字符串值
const currentTableType = ref(props.defaultTableType || "site");

// 表格类型点击处理
const handleTableTypeClick = (value: string) => {
  currentTableType.value = value;
  currentPage.value = 1; // 重置分页
};

// 监听表格类型变化，重置分页
watch(
  () => currentTableType.value,
  (newType, oldType) => {
    currentPage.value = 1; // 重置分页
  }
);

// 日期变更处理函数
const handleDateChange = (val: string) => {
  if (val) {
    currentDate.value = val;
  }
};

// 区域选择处理函数
const handleAreaChange = (value: number[]) => {
  selectedArea.value = value;
  if (props.visible) {
    fetchAttendanceData(currentDate.value);
  }
};

// 地区节点点击事件
const areaNodeClickHandle = (item: any) => {
  const areaPath = getNodePathById(item.id, userStore.userRegionTree);
  const ids = areaPath.map(node => node.id);
  selectedArea.value = ids;

  // 确保在nextTick中关闭下拉框
  nextTick(() => {
    if (areaCascaderRef.value) {
      areaCascaderRef.value.togglePopperVisible(false);
    }
    // 然后处理区域变化
    handleAreaChange(ids);
  });
};

// 回到默认节点
const backToDefaultNode = () => {
  selectedArea.value = appStore.getLargeScreenPathId || [];
  handleAreaChange(selectedArea.value);
};

// 获取地址参数
const getAddressParams = (areaIds: number[]) => {
  const params = {
    city: userStore.userInfo.city
  } as any;

  if (!areaIds?.length) return params;

  let currentNode = userStore.userRegionTree.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return params;

  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }

  return params;
};

// 根据ID查找节点
const findNodeById = (nodes: any[], id: number): any => {
  if (!nodes || !id) return undefined;

  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.childList) {
      const found = findNodeById(node.childList, id);
      if (found) return found;
    }
  }
  return undefined;
};

// 初始化默认区域选择
const initializeAreaSelection = () => {
  // 如果用户地区树已加载且没有选中区域，使用默认值
  if (userStore.userRegionTree?.length && !selectedArea.value.length) {
    const defaultPathId = appStore.getLargeScreenPathId;
    if (defaultPathId?.length) {
      selectedArea.value = defaultPathId;
    }
  }
};

// 添加展开站点的数据管理
const expandedSites = ref<Set<string>>(new Set());

// 控制站点展开/收起的方法
const toggleSite = (siteId: string) => {
  if (expandedSites.value.has(siteId)) {
    expandedSites.value.delete(siteId);
  } else {
    expandedSites.value.add(siteId);
  }
};

// 判断站点是否处于展开状态
const isExpanded = (siteId: string): boolean => {
  return expandedSites.value.has(siteId);
};

// 判断人员是否为兼职
const isPartTimeStaff = (staff: any): boolean => {
  if (staff.isPartTime !== undefined) {
    return staff.isPartTime;
  }
  return (staff as any).deptName?.includes("兼职") || false;
};

// 判断站点是否有兼职人员
const hasSitePartTimeStaff = (siteId: string): boolean => {
  const site = currentTableData.value.find(site => site.site === siteId);
  if (!site) return false;
  return site.staff.some(staff => isPartTimeStaff(staff));
};

// 获取站点的兼职人员数量
const getSitePartTimeStaffCount = (siteId: string): number => {
  const site = currentTableData.value.find(site => site.site === siteId);
  if (!site) return 0;
  return site.staff.filter(staff => isPartTimeStaff(staff)).length;
};

const handleClose = () => {
  emit("update:visible", false);
};

// 获取员工状态
const getStaffStatus = (row: any) => {
  // 首先检查员工的直接状态
  if (row.status) {
    if (isPartTimeStaff(row)) {
      const site = currentTableData.value.find(
        site => site.siteId === row.siteId
      );
      const hasFullTimeStaffOnLeave = site?.staff.some(
        staff => !isPartTimeStaff(staff) && staff.status === "请假"
      );
      if (hasFullTimeStaffOnLeave) {
        return row.status;
      }
      return row.status === "在岗" ? "在岗" : "";
    }
    return row.status;
  }
  if (!row.shifts || row.shifts.length === 0) {
    if (isPartTimeStaff(row)) return "";
    return "下班";
  }
  const now = new Date();
  const hasLeave = row.shifts.some(
    (shift: any) =>
      shift.status === "请假" || shift.statusText?.includes("请假")
  );
  if (hasLeave) {
    if (isPartTimeStaff(row)) {
      const site = currentTableData.value.find(
        site => site.siteId === row.siteId
      );
      const hasFullTimeStaffOnLeave = site?.staff.some(
        staff => !isPartTimeStaff(staff) && staff.status === "请假"
      );
      if (hasFullTimeStaffOnLeave) {
        return "请假";
      }
      return "";
    }
    return "请假";
  }
  const hasFault = row.shifts.some(
    (shift: any) =>
      shift.status === "故障" || shift.statusText?.includes("故障")
  );
  if (hasFault) {
    if (isPartTimeStaff(row)) {
      const site = currentTableData.value.find(
        site => site.siteId === row.siteId
      );
      const hasFullTimeStaffOnLeave = site?.staff.some(
        staff => !isPartTimeStaff(staff) && staff.status === "请假"
      );
      if (hasFullTimeStaffOnLeave) {
        return "故障";
      }
      return "";
    }
    return "故障";
  }
  const hasRest = row.shifts.some(
    (shift: any) =>
      shift.status === "休息" || shift.statusText?.includes("休息")
  );
  if (hasRest) {
    if (isPartTimeStaff(row)) {
      const site = currentTableData.value.find(
        site => site.siteId === row.siteId
      );
      const hasFullTimeStaffOnLeave = site?.staff.some(
        staff => !isPartTimeStaff(staff) && staff.status === "请假"
      );
      if (hasFullTimeStaffOnLeave) {
        return "休息";
      }
      return "";
    }
    return "休息";
  }
  for (const shift of row.shifts) {
    if (!shift.startTime || !shift.endTime) continue;
    const [startHour, startMinute] = shift.startTime.split(":").map(Number);
    const [endHour, endMinute] = shift.endTime.split(":").map(Number);
    if (
      isNaN(startHour) ||
      isNaN(startMinute) ||
      isNaN(endHour) ||
      isNaN(endMinute)
    )
      continue;
    const shiftStart = new Date(now);
    shiftStart.setHours(startHour, startMinute, 0, 0);
    const shiftEnd = new Date(now);
    shiftEnd.setHours(endHour, endMinute, 0, 0);
    if (now >= shiftStart && now <= shiftEnd) {
      if (shift.leavePostDetails && shift.leavePostDetails.length > 0) {
        const hasActiveLeavePost = shift.leavePostDetails.some(
          detail => detail.endTime === "至今" || new Date(detail.endTime) > now
        );
        if (hasActiveLeavePost) {
          if (isPartTimeStaff(row)) {
            const site = currentTableData.value.find(
              site => site.siteId === row.siteId
            );
            const hasFullTimeStaffOnLeave = site?.staff.some(
              staff => !isPartTimeStaff(staff) && staff.status === "请假"
            );
            if (hasFullTimeStaffOnLeave) {
              return "脱岗";
            }
            return "";
          }
          return "脱岗";
        }
      }
      if (isPartTimeStaff(row)) {
        const site = currentTableData.value.find(
          site => site.siteId === row.siteId
        );
        const hasFullTimeStaffOnLeave = site?.staff.some(
          staff => !isPartTimeStaff(staff) && staff.status === "请假"
        );
        if (hasFullTimeStaffOnLeave) {
          return "在岗";
        }
        return "在岗";
      }
      return "在岗";
    }
    if (now < shiftStart) {
      if (isPartTimeStaff(row)) {
        const site = currentTableData.value.find(
          site => site.siteId === row.siteId
        );
        const hasFullTimeStaffOnLeave = site?.staff.some(
          staff => !isPartTimeStaff(staff) && staff.status === "请假"
        );
        if (hasFullTimeStaffOnLeave) {
          return "待上班";
        }
        return "";
      }
      return "待上班";
    }
  }
  if (isPartTimeStaff(row)) {
    const site = currentTableData.value.find(
      site => site.siteId === row.siteId
    );
    const hasFullTimeStaffOnLeave = site?.staff.some(
      staff => !isPartTimeStaff(staff) && staff.status === "请假"
    );
    if (hasFullTimeStaffOnLeave) {
      return "下班";
    }
    return "";
  }
  return "下班";
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

// 站点悬停状态
const hoveredSiteId = ref<string | null>(null);

// 添加数据缓存
const dataCache = ref(new Map());
const lastTableDataHash = ref("");

// 添加延迟加载机制
const isDialogReady = ref(false);

// 新增 loading 状态和内部数据 currentTableData
const loading = ref(false);
const currentTableData = ref<any[]>([]);

// 表格高度计算
const tableHeight = computed(() => {
  // 弹窗总高度 1024px
  // 减去：边框padding 30px * 2 = 60px
  // 减去：标题区域高度 80px + margin-bottom 20px = 100px
  // 内容区域可用高度：1024 - 60 - 100 = 864px
  // 减去：分页区域高度 80px + 间距 20px = 100px
  // 减去：表格容器padding 20px * 2 = 40px
  // 剩余表格高度：864 - 100 - 40 = 724px
  return 724;
});

// 弹窗打开时重置 currentDate 为 props.defaultDate
watch(
  () => props.visible,
  async visible => {
    if (visible) {
      currentDate.value = props.defaultDate; // 每次打开都重置为父组件传入的日期
      currentTableType.value = props.defaultTableType || "site"; // 使用外部传入的默认表格类型
      currentPage.value = 1; // 重置分页

      // 确保用户地区树已初始化
      if (!userStore.userRegionTree?.length) {
        await userStore.initUserRegionTree();
      }

      // 初始化区域选择
      initializeAreaSelection();

      // 弹窗打开时直接获取全部数据，而不是使用props传入的数据
      fetchAttendanceData(currentDate.value);
    }
  }
);

// 监听用户地区树变化
watch(
  () => userStore.userRegionTree,
  newTree => {
    if (newTree?.length && props.visible) {
      initializeAreaSelection();
    }
  },
  { immediate: true }
);

// 监听defaultTableType的变化
watch(
  () => props.defaultTableType,
  newType => {
    if (props.visible && newType) {
      currentTableType.value = newType;
      currentPage.value = 1; // 重置分页
    }
  },
  { immediate: true }
);

// 2. 只要 currentDate 变化且弹窗是打开状态，就请求接口
watch(
  () => currentDate.value,
  date => {
    if (props.visible) {
      fetchAttendanceData(date);
    }
  }
);

// 监听弹窗显示和日期变化，决定数据来源
watch(
  [() => props.visible, currentDate, currentTableData],
  ([visible, date, data]) => {
    if (visible) {
      setTimeout(() => {
        isDialogReady.value = true;
      }, 100);
    } else {
      isDialogReady.value = false;
      dataCache.value.clear();
    }
  },
  { immediate: true }
);

// 独立封装获取考勤数据的方法
async function fetchAttendanceData(date: string) {
  loading.value = true;
  try {
    // 获取区域参数
    const areaParams = getAddressParams(selectedArea.value);
    const res = await getAttendance({ ...areaParams, date });
    if (res.code === 200 && res.data && res.data.sites) {
      currentTableData.value = res.data.sites.map(site => ({
        site: site.location.site, // 保证结构一致
        city: site.location.city,
        county: site.location.county,
        township: site.location.township,
        hamlet: site.location.hamlet,
        hasAbnormal: site.status !== "在岗" && site.status !== "休息",
        totalStaff: site.staff.length,
        siteId: site.siteId,
        deviceFaultRecords: site.deviceFaultRecords || [],
        staff: site.staff.map(staffMember => ({
          userId: staffMember.userId,
          name: staffMember.userName,
          phone: staffMember.phone,
          deptName: staffMember.deptName,
          hasAbnormal:
            staffMember.status !== "在岗" && staffMember.status !== "休息",
          status: staffMember.status,
          shifts: staffMember.shifts.map(shift => ({
            scheduleId: Math.random(),
            shiftName: shift.shiftName,
            startTime: shift.startTime,
            endTime: shift.endTime,
            status: shift.status,
            statusText: shift.status,
            leavePostDetails:
              shift.abnormalRecords?.map(record => ({
                startTime: record.startTime,
                endTime: record.endTime,
                duration: parseInt(record.duration) || 0,
                status: record.type
              })) || []
          }))
        }))
      }));
    }
  } catch (e) {
    currentTableData.value = [];
  } finally {
    loading.value = false;
  }
}

// 2. currentTableData 更新后，强制刷新 isDialogReady，确保时间轴刷新
watch(
  () => currentTableData.value,
  () => {
    isDialogReady.value = false;
    setTimeout(() => {
      isDialogReady.value = true;
    }, 50);
  }
);

// 优化悬停处理，使用防抖
let hoverTimer: number | null = null;

const handleSiteHover = (siteId: string, isHover: boolean) => {
  // 清除之前的定时器
  if (hoverTimer) {
    clearTimeout(hoverTimer);
  }

  // 使用防抖，减少频繁的状态更新
  hoverTimer = setTimeout(() => {
    hoveredSiteId.value = isHover ? siteId : null;
  }, 50) as unknown as number;
};

// 计算数据哈希值
const getDataHash = (data: any[]) => {
  return JSON.stringify(
    data.map(site => ({
      siteId: site.siteId,
      staffCount: site.staff?.length || 0,
      lastModified: site.lastModified || Date.now()
    }))
  );
};

// 优化后的flattenedData计算属性
const flattenedData = computed(() => {
  const currentDataHash = getDataHash(currentTableData.value);
  const cacheKey = `${currentDataHash}-${currentPage.value}-${pageSize.value}-${currentTableType.value}`;

  // 检查缓存
  if (dataCache.value.has(cacheKey)) {
    return dataCache.value.get(cacheKey);
  }

  let result: any[] = [];

  if (currentTableType.value === "site") {
    // 站点列表模式：显示站点数据
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    const paginatedSites = currentTableData.value.slice(start, end);

    paginatedSites.forEach(site => {
      // 预先计算排序，避免重复计算isPartTimeStaff
      const staffWithPartTimeFlag = site.staff.map((staff: any) => ({
        ...staff,
        _isPartTime: isPartTimeStaff(staff) // 预计算兼职标识
      }));

      // 使用预计算的标识进行排序
      const sortedStaff = staffWithPartTimeFlag.sort((a, b) => {
        // 专职排在前面(false)，兼职排在后面(true)
        if (a._isPartTime && !b._isPartTime) return 1;
        if (!a._isPartTime && b._isPartTime) return -1;
        return 0;
      });

      // 使用排序后的员工数据
      sortedStaff.forEach(staff => {
        result.push({
          ...staff,
          site: site.site,
          township: site.township,
          hamlet: site.hamlet,
          siteHasAbnormal: site.hasAbnormal,
          siteId: site.siteId,
          status: staff.status // 显式传递整体状态
        });
      });
    });
  } else {
    // 人员列表模式：根据状态过滤所有人员
    const allStaff: any[] = [];

    currentTableData.value.forEach(site => {
      site.staff.forEach(staff => {
        const staffStatus = getStaffStatus({
          ...staff,
          siteId: site.siteId,
          hamlet: site.hamlet,
          township: site.township
        });

        let shouldInclude = false;
        switch (currentTableType.value) {
          case "on-duty":
            shouldInclude = staffStatus === "在岗";
            break;
          case "off-duty":
            shouldInclude = staffStatus === "脱岗";
            break;
          case "rest":
            shouldInclude =
              staffStatus === "休息" ||
              staffStatus === "下班" ||
              staffStatus === "请假";
            break;
          case "fault":
            shouldInclude = staffStatus === "故障";
            break;
        }

        if (shouldInclude) {
          allStaff.push({
            ...staff,
            site: site.site,
            township: site.township,
            hamlet: site.hamlet,
            siteHasAbnormal: site.hasAbnormal,
            siteId: site.siteId,
            status: staff.status,
            calculatedStatus: staffStatus // 添加计算出的状态
          });
        }
      });
    });

    // 对人员列表进行分页
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    result = allStaff.slice(start, end);
  }

  // 缓存结果
  dataCache.value.set(cacheKey, result);

  // 清理旧缓存，避免内存泄露
  if (dataCache.value.size > 10) {
    const keys = Array.from(dataCache.value.keys());
    keys.slice(0, 5).forEach(key => dataCache.value.delete(key));
  }

  return result;
});

// 添加新的计算属性，用于按站点合并班次数据
const siteShiftsMap = computed(() => {
  const result = new Map<string, any[]>();

  // 按站点ID分组员工的班次数据
  flattenedData.value.forEach(item => {
    if (!result.has(item.siteId)) {
      result.set(item.siteId, []);
    }

    // 将该员工的班次添加到对应站点的数组中
    const staffShifts = item.shifts.map((shift: any) => ({
      ...shift,
      staffName: item.name, // 添加员工姓名，方便后续显示
      staffId: item.userId // 添加员工ID，作为唯一标识
    }));

    result.get(item.siteId)?.push(...staffShifts);
  });

  return result;
});

// 判断是否是站点的第一行
const isSiteFirstRow = (row: any) => {
  const index = flattenedData.value.findIndex(item => item === row);
  if (index <= 0) return true;

  // 如果当前行与前一行的站点ID不同，则是站点的第一行
  return flattenedData.value[index - 1].siteId !== row.siteId;
};

// 获取站点的班次数据
const getSiteShifts = (siteId: string) => {
  // 查找对应的站点数据
  const site = currentTableData.value.find(site => site.site === siteId);

  // 如果找到站点并且有 allShifts 数据，则直接使用
  if (site && site.allShifts) {
    return site.allShifts;
  }

  // 如果没有 allShifts，则使用原有逻辑
  return siteShiftsMap.value.get(siteId) || [];
};

// 修改分页器的总数根据表格类型变化
const pagination = computed(() => {
  let total = 0;

  if (currentTableType.value === "site") {
    // 站点列表模式：使用站点总数
    total = currentTableData.value.length;
  } else {
    // 人员列表模式：计算符合条件的人员总数
    let totalStaff = 0;
    currentTableData.value.forEach(site => {
      site.staff.forEach(staff => {
        const staffStatus = getStaffStatus({
          ...staff,
          siteId: site.siteId,
          hamlet: site.hamlet,
          township: site.township
        });

        let shouldInclude = false;
        switch (currentTableType.value) {
          case "on-duty":
            shouldInclude = staffStatus === "在岗";
            break;
          case "off-duty":
            shouldInclude = staffStatus === "脱岗";
            break;
          case "rest":
            shouldInclude =
              staffStatus === "休息" ||
              staffStatus === "下班" ||
              staffStatus === "请假";
            break;
          case "fault":
            shouldInclude = staffStatus === "故障";
            break;
        }

        if (shouldInclude) {
          totalStaff++;
        }
      });
    });
    total = totalStaff;
  }

  return {
    total,
    currentPage: currentPage.value,
    pageSize: pageSize.value
  };
});

// 处理单元格合并
const handleSpanMethod = ({ row, column, rowIndex }: any) => {
  // 只有在站点模式下才进行合并
  if (
    currentTableType.value === "site" &&
    (column.label === "劝导站" || column.label === "上岗情况")
  ) {
    const siteId = row.siteId;
    // 获取当前行之前有多少相同站点的行
    let prevRows = 0;
    for (let i = 0; i < rowIndex; i++) {
      if (flattenedData.value[i].siteId === siteId) {
        prevRows++;
      }
    }
    // 获取当前站点总共有多少行
    const siteRows = flattenedData.value.filter(
      item => item.siteId === siteId
    ).length;

    if (prevRows === 0) {
      // 第一行显示，后面的行合并
      return {
        rowspan: siteRows,
        colspan: 1
      };
    } else {
      // 其他行隐藏
      return {
        rowspan: 0,
        colspan: 0
      };
    }
  }
  return {
    rowspan: 1,
    colspan: 1
  };
};

// 分页事件处理
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 添加新的计算属性，用于获取行类名
const getRowClassName = (row: any) => {
  const classes = [];

  if (isSiteFirstRow(row)) {
    classes.push("site-first-row");
  }

  // 如果是悬停的站点组，添加悬停类名
  if (hoveredSiteId.value && row.siteId === hoveredSiteId.value) {
    classes.push("site-group-hovered");
  }

  return classes.join(" ");
};

// 获取站点设备故障记录
const getSiteDeviceFaultRecords = (siteId: string) => {
  const site = currentTableData.value.find(site => site.site === siteId);
  if (!site || !site.deviceFaultRecords) return [];
  return site.deviceFaultRecords;
};

// 添加新的计算属性，用于获取行样式
const getRowStyle = ({ row, rowIndex }: { row: any; rowIndex: number }) => {
  const baseStyle = {
    transition: "all 0.3s ease"
  };

  // 如果是悬停的站点组，返回悬停样式
  if (hoveredSiteId.value && row.siteId === hoveredSiteId.value) {
    return {
      ...baseStyle,
      backgroundColor: "rgba(25, 80, 150, 0.8) !important"
    };
  }

  return baseStyle;
};

// 添加新的计算属性，用于获取单元格样式
const getCellStyle = ({
  row,
  column,
  rowIndex,
  columnIndex
}: {
  row: any;
  column: any;
  rowIndex: number;
  columnIndex: number;
}) => {
  const baseStyle = {
    background: "transparent",
    color: "#fff",
    borderBottom: "1px solid rgba(64, 158, 255, 0.2)",
    padding: "8px 0"
  };

  // 如果是悬停的站点组，返回悬停样式
  if (hoveredSiteId.value && row.siteId === hoveredSiteId.value) {
    return {
      ...baseStyle,
      backgroundColor: "rgba(25, 80, 150, 0.8) !important"
    };
  }

  return baseStyle;
};
</script>
<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes loading-spin {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }

  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  padding: 20px;
}

.dialog-content {
  position: absolute; // 更改为绝对定位，保证居中
  left: 50%; // 居中定位
  top: 50%; // 居中定位
  transform: translate(-50%, -50%); // 居中定位，不进行缩放
  width: 1792px; // 根据需求调整宽度，设计定稿宽度2560px，根据弹窗大小调整宽度，小于2560
  height: 1024px; // 根据需求调整高度，设计定稿高度1271px，根据弹窗大小调整高度，小于1271
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 30px;
}

// 确保 dv-border-box-12 组件背景透明
:deep(.dv-border-box-12) {
  background: transparent !important;

  .border-box-content {
    background: transparent !important;
  }

  svg {
    background: transparent !important;
  }
}

// 标题区域
.header-section {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  height: 80px; // 固定高度
  padding: 0 20px;
  margin-bottom: 20px;
  background: rgb(0 24 75 / 80%);
  border-bottom: 2px solid rgb(64 158 255 / 30%);
  border-radius: 8px;
}

.title-area {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center; // 让选择器居中显示

  // 表格类型选择器样式
  .table-type-selector {
    height: 68px;

    .custom-segmented {
      display: flex;
      align-items: center;
      height: 100%;
      gap: 4px;
      padding: 4px;
      background: rgb(15 35 85 / 90%);
      border: 2px solid rgb(64 158 255 / 80%);
      border-radius: 12px;
      box-shadow:
        0 6px 16px rgb(0 0 0 / 50%),
        0 0 25px rgb(64 158 255 / 40%);

      .segmented-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 0 16px;
        margin: 0;
        @extend .fs-attendance-detail-table-type-selector; // 上岗情况弹窗表格类型选择器按钮字体大小（列表选择）
        font-weight: 400; // 调整字重
        line-height: 1;
        color: #fff;
        text-align: center;
        text-shadow: none; // 移除文字阴影
        letter-spacing: 0; // 移除字间距
        vertical-align: middle;
        cursor: pointer;
        background: transparent;
        border: 1px solid transparent; // 减小边框宽度
        border-radius: 4px; // 减小圆角
        outline: none;
        transition: all 0.3s ease;

        &:hover:not(.active) {
          background: rgb(64 158 255 / 30%);
          border-color: rgb(64 158 255 / 60%);
          box-shadow: 0 2px 4px rgb(64 158 255 / 40%);
          transform: translateY(-1px);
        }

        &.active {
          font-weight: 500;
          color: #fff;
          text-shadow: none; // 移除文字阴影
          background: #409eff; // 使用纯色背景
          border: 1px solid #fff; // 减小边框宽度
          box-shadow: 0 2px 6px rgb(64 158 255 / 40%);
          transform: translateY(-1px);
        }
      }
    }
  }
}

.controls-area {
  display: flex;
  gap: 20px; // 调整为合适的间距
  align-items: center;

  // 区域选择器样式
  .area-selector-section {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .date-selector {
    // 使用 CustomDatePicker 组件，无需额外样式
  }

  .close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 34px; // 从40px减少到34px
    height: 34px; // 从40px减少到34px
    cursor: pointer;
    background: rgb(245 34 45 / 10%);
    border: 1px solid rgb(245 34 45 / 30%);
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      background: rgb(245 34 45 / 20%);
      border-color: rgb(245 34 45 / 50%);
      transform: scale(1.1);
    }

    .el-icon {
      @extend .fs-illegal-close; // 关闭按钮图标字体大小
      color: #f56c6c;
    }
  }
}

// 内容区域
.content-section {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 20px; // 内容区域与分页区域间距：20px
  min-height: 0;
  overflow: hidden;
  height: 864px; // 内容区域高度：864px（1024px - 60px边框 - 100px标题）
}

// 表格容器
.table-container {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  height: 764px; // 表格容器高度：764px（864px - 80px分页区域高度 - 20px间距）
  background: rgb(0 24 75 / 40%);
  border: 1px solid rgb(64 158 255 / 20%);
  border-radius: 8px;

  :deep(.el-table) {
    --el-table-border-color: transparent;
    --el-table-border: none;
    --el-table-bg-color: transparent;
    --el-table-tr-bg-color: transparent;
    --el-table-header-bg-color: transparent;
    --el-table-row-hover-bg-color: transparent;

    height: 100% !important;
    max-height: 100% !important; // 确保表格不超过容器高度
    background: transparent !important;
    border: none;
    overflow: hidden; // 防止表格本身溢出

    &::before,
    &::after {
      display: none;
    }

    .el-table__inner-wrapper {
      height: 100% !important;
      max-height: 100% !important; // 确保内部包装器不超过容器高度
      background: transparent !important;
      overflow: hidden; // 防止内部包装器溢出

      &::after {
        display: none;
      }
    }

    // 表头样式
    .el-table__header-wrapper {
      background: transparent !important;

      th.el-table__cell {
        position: relative;
        padding: 12px 0; // 表格表头内边距：12px（固定）
        @extend .fs-attendance-detail-table-header; // 上岗情况弹窗表格表头字体大小
        font-weight: 600;
        color: #fff !important;
        text-shadow: 0 0 5px rgb(255 255 255 / 30%);
        background: rgb(2 48 115 / 80%) !important;
        border: none !important;

        &::after {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          height: 2px;
          content: "";
          background: linear-gradient(
            90deg,
            rgb(64 158 255 / 20%) 0%,
            rgb(64 158 255 / 80%) 50%,
            rgb(64 158 255 / 20%) 100%
          );
        }
      }
    }

    // 表格内容样式
    .el-table__body-wrapper {
      background: transparent !important;
      max-height: 664px; // 表格内容最大高度：664px（724px - 60px表头高度）
      overflow-y: auto; // 启用垂直滚动

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(64, 158, 255, 0.3);
        border-radius: 4px;

        &:hover {
          background: rgba(64, 158, 255, 0.5);
        }
      }
    }

    // 表格空状态样式
    .el-table__empty-block {
      background: transparent !important;

      .el-table__empty-text {
        @extend .fs-attendance-detail-other-status; // 表格空状态字体大小：18px（增大字体）
        color: rgb(255, 255, 255, 0.6) !important;
      }
    }

    .el-table__body-wrapper {
      td.el-table__cell {
        position: relative;
        padding: 10px 0; // 表格单元格内边距：10px（固定）
        // font-size: 16px; // 表格单元格字体大小：16px（固定）(不起作用)
        line-height: 1.5; // 表格单元格行高：1.5（固定）
        color: rgb(255 255 255 / 80%) !important;
        vertical-align: middle;
        background: transparent !important;
        border: none !important;
        transition: all 0.3s;

        &::after {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          height: 1px;
          content: "";
          background: linear-gradient(
            90deg,
            rgb(64 158 255 / 5%) 0%,
            rgb(64 158 255 / 30%) 50%,
            rgb(64 158 255 / 5%) 100%
          );
        }
      }

      // 行样式
      .el-table__row {
        background: transparent !important;
        border-bottom: 2px solid rgb(64 158 255 / 15%);
        transition: all 0.3s ease;

        &:nth-child(odd) {
          background: rgb(64 158 255 / 8%) !important;
        }

        &:nth-child(even) {
          background: rgb(0 24 75 / 30%) !important;
        }

        &:hover {
          z-index: 2;
          background: rgb(64 158 255 / 25%) !important;
          box-shadow: 0 0 15px rgb(64 158 255 / 30%) inset;
          transform: translateX(3px);
        }

        // 站点组悬停效果
        &.site-group-hovered {
          background: rgb(25 80 150 / 40%) !important;

          td.el-table__cell {
            background: rgb(25 80 150 / 40%) !important;
          }
        }
      }
    }
  }
}

// 分页区域
.pagination-section {
  display: flex;
  flex-shrink: 0; // 确保分页区域不被压缩
  justify-content: center;
  align-items: center;
  height: 80px; // 固定分页区域高度：80px
  padding: 0 20px;
  background: rgb(0 24 75 / 30%);
  border: 1px solid rgb(64 158 255 / 10%);
  border-radius: 8px;

  :deep(.el-pagination) {
    .el-pagination__total,
    .el-pagination__jump,
    .el-pagination__sizes {
      @extend .fs-attendance-detail-pagination; // 上岗情况弹窗分页器字体大小
      color: rgb(255 255 255 / 80%);
    }

    .btn-prev,
    .btn-next,
    .el-pager li {
      min-width: 28px; // 固定尺寸
      height: 28px; // 固定尺寸
      @extend .fs-attendance-detail-pagination; // 上岗情况弹窗分页器字体大小
      line-height: 28px; // 固定行高
      color: #ffffff5c;
      background: rgb(64 158 255 / 5%);
      border: 1px solid rgb(64 158 255 / 10%);
      transition: all 0.3s;

      &:hover:not([disabled]) {
        color: #409eff;
        background: rgb(64 158 255 / 10%);
        transform: translateY(-1px);
      }

      &.is-active {
        font-weight: 500;
        color: #409eff !important;
        background: rgb(64 158 255 / 15%);
        border-color: rgb(64 158 255 / 30%);
      }
    }
  }
}

// 站点信息样式
.site-info {
  position: relative;
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
  padding: 8px 10px;
  margin: 0 5px;
  font-weight: 500;
  color: #fff;
  background: rgb(64 158 255 / 10%);
  border-left: 3px solid #409eff;
  border-radius: 4px;
}

// 时间轴相关样式
.leave-info {
  padding: 8px;
  background: rgb(0 24 75 / 30%);
  border: 1px solid rgb(64 158 255 / 20%);
  border-radius: 4px;

  .timeline-section {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.timeline-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  margin: 8px 0;
  @extend .fs-attendance-detail-other-status; // 其余状态字体大小
  color: rgb(255 255 255 / 60%);
  background: rgb(0 24 75 / 20%);
  border-radius: 4px;

  span {
    position: relative;

    &::after {
      position: absolute;
      top: 50%;
      right: -20px;
      width: 14px;
      height: 14px;
      content: "";
      border: 2px solid rgb(64 158 255 / 30%);
      border-top-color: #409eff;
      border-radius: 50%;
      transform: translateY(-50%);
      animation: loading-spin 1s linear infinite;
    }
  }
}

// 空状态样式
.empty-status {
  display: inline-block;
  padding: 6px 10px;
  font-size: 18px; // 空状态字体大小：18px（增大字体）
  font-style: italic;
  color: rgb(255 255 255 / 30%);
}

// 单元格悬停包装器
.cell-hover-wrapper {
  position: absolute;
  inset: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  @extend .fs-attendance-detail-table-header; // 单元格内容字体
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: rgb(64 158 255 / 10%);
  }
}

.empty-cell-hover {
  width: 100%;
  height: 48px;
  min-height: 48px;
  cursor: pointer;

  &:hover {
    background-color: rgb(64 158 255 / 10%);
  }
}

// 站点行样式
.site-first-row {
  position: relative;

  &::before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 6px;
    content: "";
    background: linear-gradient(180deg, #409eff, #1890ff);
    border-radius: 0 4px 4px 0;
    box-shadow: 2px 0 8px rgb(64 158 255 / 30%);
  }

  td {
    background: rgb(25 80 150 / 25%) !important;
    border-top: 3px solid rgb(64 158 255 / 40%) !important;

    &:first-child {
      border-left: 6px solid #409eff !important;
    }
  }

  .site-info {
    font-size: 18px !important; // 悬停状态站点信息字体大小：18px（固定）
    font-weight: 600 !important;
    text-shadow: 0 0 6px rgb(64 158 255 / 50%) !important;
    background: linear-gradient(
      135deg,
      rgb(64 158 255 / 20%),
      rgb(64 158 255 / 5%)
    ) !important;
    border: 1px solid rgb(64 158 255 / 40%) !important;
    border-left: 4px solid #409eff !important;
    box-shadow: 0 2px 8px rgb(0 0 0 / 20%) !important;
  }
}

tr:not(.site-first-row) {
  position: relative;

  &::before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 2px;
    content: "";
    background: rgb(64 158 255 / 30%);
  }

  td {
    background: rgb(25 80 150 / 8%) !important;
    border-left: 2px solid rgb(64 158 255 / 20%) !important;

    &:first-child {
      border-left: 2px solid rgb(64 158 255 / 30%) !important;
    }
  }
}

.status-text[data-status="在岗"] {
  font-weight: 600;
  color: #52c41a;
}

.status-text[data-status="脱岗"] {
  font-weight: 600;
  color: #faad14;
}

.status-text[data-status="故障"] {
  font-weight: 600;
  color: #f5222d;
}

.status-text[data-status="请假"],
.status-text[data-status="下班"],
.status-text[data-status="休息"] {
  font-weight: 600;
  color: #bfbfbf;
}

.status-text {
  display: inline-block;
  padding: 0 8px;
  @extend .fs-attendance-detail-table-header; // 单元格状态字体大小
  background: none !important;
  border: none !important;
  border-radius: 0;
}
</style>
<style lang="scss"></style>
