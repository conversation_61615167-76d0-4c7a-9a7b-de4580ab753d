<template>
  <el-dialog
    v-model="dialogVisible"
    title="实时监控"
    width="70%"
    class="point-alarm-video-dialog"
    :destroy-on-close="true"
    @closed="handleClose"
  >
    <div class="video-container">
      <video ref="videoRef" class="video-player" controls autoplay />
      <WebRtc ref="webRtcRef" :roomId="deviceId" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import WebRtc from "@/components/WebRtc/index.vue";
import flvjs from "flv.js";
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  streamKey: {
    type: String,
    default: ""
  },
  deviceId: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(["update:visible"]);
const dialogVisible = ref(props.visible);
const videoRef = ref<HTMLVideoElement | null>(null);
const flvPlayer = ref<flvjs.Player | null>(null);

const destroyPlayer = () => {
  if (flvPlayer.value) {
    flvPlayer.value.unload();
    flvPlayer.value.detachMediaElement();
    flvPlayer.value.destroy();
    flvPlayer.value = null;
  }
};

const initializePlayer = async () => {
  if (!flvjs.isSupported()) {
    console.error("您的浏览器不支持 FLV 播放");
    return;
  }

  // 等待 DOM 更新
  await nextTick();

  if (!videoRef.value || !props.streamKey) {
    console.error("视频元素或流地址不存在");
    return;
  }

  // 确保在初始化新播放器前销毁旧的
  destroyPlayer();

  try {
    flvPlayer.value = flvjs.createPlayer({
      type: "flv",
      url: props.streamKey,
      isLive: true
    });

    flvPlayer.value.attachMediaElement(videoRef.value);

    flvPlayer.value.on(flvjs.Events.ERROR, (errType, errDetail) => {
      console.error("播放器错误:", errType, errDetail);
    });

    flvPlayer.value.load();
    flvPlayer.value.on(flvjs.Events.LOADING_COMPLETE, () => {
      if (videoRef.value && flvPlayer.value) {
        const playPromise = videoRef.value.play();
        if (playPromise) {
          playPromise.catch(err => {
            console.error("播放失败:", err);
          });
        }
      }
    });
  } catch (error) {
    console.error("播放器初始化失败:", error);
  }
};

const handleClose = () => {
  destroyPlayer();
  emit("update:visible", false);
};

// 监听对话框可见性变化
watch(
  () => props.visible,
  async val => {
    dialogVisible.value = val;
    if (val) {
      await initializePlayer();
    } else {
      destroyPlayer();
    }
  }
);

// 监听对话框内部状态变化
watch(
  () => dialogVisible.value,
  val => {
    emit("update:visible", val);
    if (!val) {
      destroyPlayer();
    }
  }
);

// 监听流地址变化
watch(
  () => props.streamKey,
  () => {
    if (dialogVisible.value) {
      initializePlayer();
    }
  }
);

onMounted(async () => {
  if (props.visible) {
    await initializePlayer();
  }
});

onBeforeUnmount(() => {
  destroyPlayer();
});
</script>

<style scoped>
.video-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60% !important;
  overflow: hidden;
  background: #000;
  border-radius: 4px;

  ::v-deep .webrtc-container {
    position: absolute;
    right: 50px;
    bottom: 100px;
  }
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>

<style lang="scss">
.point-alarm-video-dialog {
  :deep(.el-dialog__header) {
    .el-dialog__headerbtn {
      top: 12px !important;
      right: 12px !important;
      width: 32px !important;
      height: 32px !important;
      background: rgb(255 255 255 / 10%) !important;
      border-radius: 4px !important;
      transition: all 0.3s !important;

      &:hover {
        background: rgb(255 255 255 / 20%) !important;
        transform: scale(1.1) !important;
      }

      .el-dialog__close {
        font-size: 20px !important;
        font-weight: bold !important;
        color: rgb(255 255 255 / 80%) !important;

        &:hover {
          color: #f56c6c !important;
        }
      }
    }
  }
}
</style>
