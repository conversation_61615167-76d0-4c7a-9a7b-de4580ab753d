export default {
  path: "/counselor",
  redirect: "/illegal/log",
  meta: {
    icon: "tdesign:user-list-filled",
    title: "劝导员管理",
    rank: 5
  },
  children: [
    {
      path: "/illegal/log",
      name: "IllegalLog",
      component: () => import("@/views/illegal/illegal-persuasion/index.vue"),
      meta: {
        title: "违法处理"
      }
    },
    {
      path: "/illegal/dispatch",
      name: "IllegalDispatch",
      component: () => import("@/views/illegal/illegal-dispatch/index.vue"),
      meta: {
        title: "违法分派"
      }
    },
    {
      path: "/attendance-management",
      name: "AttendanceManagement",
      component: () => import("@/views/attendance-management/index.vue"),
      meta: {
        title: "考勤列表"
      }
    },
    {
      path: "/schedule-management",
      name: "ScheduleManagement",
      component: () => import("@/views/schedule-management/index.vue"),
      meta: {
        title: "排班列表"
      }
    },
    {
      path: "/face-review",
      name: "FaceReview",
      component: () => import("@/views/face-review/index.vue"),
      meta: {
        title: "人脸审核"
      }
    },
    {
      path: "/examine-leave",
      name: "ExamineLeave",
      component: () => import("@/views/illegal/examine-leave/index.vue"),
      meta: {
        title: "请假审核"
      }
    }
  ]
} satisfies RouteConfigsTable;
